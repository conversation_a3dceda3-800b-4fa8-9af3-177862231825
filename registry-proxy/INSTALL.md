# Registry Proxy Installation Guide

## Prerequisites

### 1. Kubernetes Cluster Requirements

- Kubernetes version 1.19 or higher
- Nodes with GPU hardware
- GPU drivers installed on nodes
- Helm 3.2.0 or higher

### 2. Node Preparation

Ensure your GPU nodes are properly labeled:

```bash
# Label GPU nodes
kubectl label nodes <gpu-node-name> gpu.enable=true

# Verify labels
kubectl get nodes --show-labels | grep gpu.enable
```

### 3. Verify GPU Drivers

Check that GPU drivers are installed on nodes:

```bash
# On each GPU node, verify NVIDIA drivers (if using NVIDIA GPUs)
nvidia-smi

# Or check for other GPU vendors
lspci | grep -i gpu
```

## Installation Methods

### Method 1: Basic Installation

```bash
# Install with default settings
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace
```

### Method 2: Production Installation

```bash
# Install with production configuration
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --values examples/values-production.yaml \
  --set image.tag=v1.0.0
```

### Method 3: Development Installation

```bash
# Install for development/testing
helm install registry-proxy ./charts/registry-proxy \
  --namespace registry-proxy-dev \
  --create-namespace \
  --values examples/values-development.yaml
```

### Method 4: Custom Configuration

Create your own values file:

```yaml
# custom-values.yaml
image:
  tag: "v1.0.0"
  pullPolicy: IfNotPresent

container:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 256Mi

nodeSelector:
  gpu.enable: "true"
  gpu.type: "nvidia"

healthcheck:
  enabled: true
```

Then install:

```bash
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --values custom-values.yaml
```

## Post-Installation Verification

### 1. Check DaemonSet Status

```bash
kubectl get daemonset registry-proxy -n torin-system
```

Expected output:
```
NAME                  DESIRED   CURRENT   READY   UP-TO-DATE   AVAILABLE   NODE SELECTOR       AGE
registry-proxy   3         3         3       3            3           gpu.enable=true    2m
```

### 2. Verify Pods are Running

```bash
kubectl get pods -n torin-system -l app=registry-proxy
```

### 3. Check Device Registration

```bash
# Check if devices are visible to Kubernetes
kubectl get nodes -o json | jq '.items[].status.allocatable' | grep -i gpu

# Or use describe to see more details
kubectl describe nodes | grep -A 10 "Allocatable"
```

### 4. View Logs

```bash
kubectl logs -f daemonset/registry-proxy -n torin-system
```

## Testing Device Allocation

Create a test pod to verify device allocation works:

```bash
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: gpu-test-pod
spec:
  containers:
  - name: gpu-container
    image: nvidia/cuda:11.0-base
    command: ["sleep", "3600"]
    resources:
      limits:
        nvidia.com/gpu: 1
  nodeSelector:
    gpu.enable: "true"
EOF
```

Check if the pod is scheduled and running:

```bash
kubectl get pod gpu-test-pod -o wide
kubectl describe pod gpu-test-pod
```

Clean up the test pod:

```bash
kubectl delete pod gpu-test-pod
```

## Configuration Examples

### High Availability Setup

```yaml
# values-ha.yaml
daemonset:
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1

container:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 256Mi

healthcheck:
  enabled: true
  livenessProbe:
    initialDelaySeconds: 60
    periodSeconds: 30
  readinessProbe:
    initialDelaySeconds: 10
    periodSeconds: 10

priorityClassName: "system-node-critical"
```

### Multi-GPU Type Support

```yaml
# values-multi-gpu.yaml
nodeSelector:
  gpu.enable: "true"

# Use affinity for more complex scheduling
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: gpu.enable
          operator: In
          values: ["true"]
        - key: gpu.type
          operator: In
          values: ["nvidia", "amd", "intel"]
```

## Upgrade

```bash
# Upgrade to new version
helm upgrade registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --values your-values.yaml

# Upgrade with new image tag
helm upgrade registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --set image.tag=v1.1.0 \
  --reuse-values
```

## Uninstall

```bash
# Uninstall the registry proxy
helm uninstall registry-proxy --namespace torin-system

# Clean up namespace if no other resources
kubectl delete namespace torin-system
```

## Troubleshooting

### Issue: Pods not scheduled on GPU nodes

**Solution:**
```bash
# Check node labels
kubectl get nodes --show-labels | grep gpu

# Add missing labels
kubectl label nodes <node-name> gpu.enable=true
```

### Issue: Device plugin not registering devices

**Solution:**
```bash
# Check logs
kubectl logs daemonset/registry-proxy -n torin-system

# Check device plugin socket directory
kubectl exec -it <pod-name> -n torin-system -- ls -la /var/lib/kubelet/registry-proxys/

# Restart the DaemonSet
kubectl rollout restart daemonset/registry-proxy -n torin-system
```

### Issue: Permission denied errors

**Solution:**
```bash
# Ensure privileged mode is enabled
helm upgrade registry-proxy ./charts/registry-proxy \
  --set container.securityContext.privileged=true \
  --reuse-values
```

For more troubleshooting tips, see the [README.md](README.md) file.
