# Registry Proxy Helm Chart

A Helm chart for deploying the Registry Proxy, a Kubernetes device plugin that manages GPU and other hardware resources on cluster nodes.

## Description

The Registry Proxy is a DaemonSet that runs on GPU-enabled nodes to:

- **Register hardware devices** with the Kubernetes scheduler
- **Manage device allocation** for pods requesting GPU resources
- **Monitor device health** and availability
- **Provide device topology** information to the kubelet

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- Nodes with GPU hardware and appropriate drivers
- Nodes labeled with `gpu.enable: "true"`

## Installation

### Quick Start

```bash
# Install with default values
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace

# Install with custom image tag
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --set image.tag=v1.0.0
```

### Production Installation

```bash
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --values examples/values-production.yaml
```

### Development Installation

```bash
helm install registry-proxy ./charts/registry-proxy \
  --namespace registry-proxy-dev \
  --create-namespace \
  --values examples/values-development.yaml
```

## Configuration

### Key Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `image.registry` | Container registry | `harbor.intra.moments8.com` |
| `image.repository` | Image repository | `moments8/apps/registry-proxy` |
| `image.tag` | Image tag | `""` (uses appVersion) |
| `image.pullPolicy` | Image pull policy | `Always` |

### Node Selection

| Parameter | Description | Default |
|-----------|-------------|---------|
| `nodeSelector` | Node selector for pod placement | `{"gpu.enable": "true"}` |
| `tolerations` | Tolerations for node taints | See values.yaml |
| `affinity` | Affinity rules | `{}` |

### Container Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `container.securityContext.privileged` | Run with privileged access | `true` |
| `container.env` | Container environment variables | `[{"name": "NODE_NAME", "valueFrom": {"fieldRef": {"fieldPath": "spec.nodeName"}}}]` |
| `container.ports` | Container ports configuration | `[{"containerPort": 5000, "hostPort": 5000}]` |
| `container.args` | Container arguments | `["--listen-addr=:5000"]` |
| `container.resources.requests.cpu` | CPU request | `50m` |
| `container.resources.requests.memory` | Memory request | `64Mi` |
| `container.resources.limits.cpu` | CPU limit | `200m` |
| `container.resources.limits.memory` | Memory limit | `128Mi` |
| `extraEnv` | Additional environment variables | `[]` |

### Volume Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `volumes.devicePlugin.hostPath` | Device plugin socket directory | `/var/lib/kubelet/registry-proxys` |
| `volumes.podResources.hostPath` | Pod resources directory | `/data/lib/kubelet/pod-resources` |

### DaemonSet Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `daemonset.revisionHistoryLimit` | Revision history limit | `5` |
| `daemonset.updateStrategy.type` | Update strategy | `RollingUpdate` |
| `daemonset.updateStrategy.rollingUpdate.maxUnavailable` | Max unavailable pods | `1` |

### ConfigMap Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `configMap.enabled` | Enable ConfigMap creation | `true` |
| `configMap.name` | ConfigMap name (auto-generated if empty) | `""` |
| `configMap.data` | ConfigMap data content | See values.yaml |
| `volumes.config.mountPath` | ConfigMap mount path in container | `/etc/registry-proxy` |

### Health Checks

| Parameter | Description | Default |
|-----------|-------------|---------|
| `healthcheck.enabled` | Enable health checks | `false` |
| `healthcheck.livenessProbe.initialDelaySeconds` | Liveness probe initial delay | `30` |
| `healthcheck.readinessProbe.initialDelaySeconds` | Readiness probe initial delay | `5` |

## Usage

### Verify Installation

```bash
# Check DaemonSet status
kubectl get daemonset registry-proxy -n torin-system

# Check pods on GPU nodes
kubectl get pods -n torin-system -o wide

# View logs
kubectl logs -f daemonset/registry-proxy -n torin-system
```

### Check Device Registration

```bash
# Verify devices are registered with nodes
kubectl get nodes -o json | jq '.items[].status.allocatable'

# Check device plugin sockets
kubectl exec -it <pod-name> -n torin-system -- ls -la /var/lib/kubelet/registry-proxys/
```

### Configure Ports and Arguments

Customize container ports and arguments:

```bash
# Install with custom port configuration
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --set container.ports[0].containerPort=8080 \
  --set container.ports[0].hostPort=8080

# Install with custom arguments
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --set-string 'container.args[0]=--listen-addr=:8080' \
  --set-string 'container.args[1]=--log-level=debug'
```

Or customize inline with values:

```yaml
container:
  ports:
    - containerPort: 8080
      hostPort: 8080
      protocol: TCP
  args:
    - --listen-addr=:8080
    - --log-level=debug
    - --config-file=/etc/config/config.yaml
```

### Configure Environment Variables

Add custom environment variables to the device plugin:

```bash
# Install with additional environment variables
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --values examples/values-with-env.yaml
```

Or add environment variables inline:

```bash
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --set-string 'extraEnv[0].name=LOG_LEVEL,extraEnv[0].value=debug' \
  --set-string 'extraEnv[1].name=PLUGIN_NAME,extraEnv[1].value=my-plugin'
```

### Configure Device Weight Mapping

Customize device weight mapping rules using ConfigMap:

```bash
# Install with custom configuration
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --values examples/values-custom-config.yaml
```

Or customize inline:

```bash
helm install registry-proxy ./charts/registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --set-file configMap.data.conversion-rules\.yaml=./my-rules.yaml
```

### Test Device Allocation

Create a test pod that requests GPU resources:

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: gpu-test
spec:
  containers:
  - name: gpu-container
    image: nvidia/cuda:11.0-base
    resources:
      limits:
        nvidia.com/gpu: 1
  nodeSelector:
    gpu.enable: "true"
```

## Troubleshooting

### Common Issues

1. **Pods not scheduled on GPU nodes**
   ```bash
   kubectl get nodes --show-labels | grep gpu.enable
   ```

2. **Device plugin not registering**
   ```bash
   kubectl logs daemonset/registry-proxy -n torin-system
   kubectl describe nodes | grep -A 10 "Allocated resources"
   ```

3. **Permission issues**
   ```bash
   kubectl describe pod <pod-name> -n torin-system
   ```

### Debug Commands

```bash
# Check node labels
kubectl get nodes --show-labels

# Check tolerations and node taints
kubectl describe nodes | grep -A 5 Taints

# Check device plugin sockets
kubectl exec -it <pod-name> -n torin-system -- ls -la /var/lib/kubelet/registry-proxys/

# Check kubelet logs on nodes
journalctl -u kubelet -f
```

## Uninstalling

```bash
helm uninstall registry-proxy --namespace torin-system
```

## Contributing

Please read the contributing guidelines before submitting pull requests.

## License

This chart is licensed under the Apache 2.0 License.
