{{/*
Expand the name of the chart.
*/}}
{{- define "registry-proxy.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "registry-proxy.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "registry-proxy.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "registry-proxy.labels" -}}
helm.sh/chart: {{ include "registry-proxy.chart" . }}
{{ include "registry-proxy.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- with .Values.additionalLabels }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "registry-proxy.selectorLabels" -}}
app.kubernetes.io/name: {{ include "registry-proxy.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app: {{ include "registry-proxy.name" . }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "registry-proxy.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "registry-proxy.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the namespace to use
*/}}
{{- define "registry-proxy.namespace" -}}
{{- .Release.Namespace }}
{{- end }}

{{/*
Create the image name
*/}}
{{- define "registry-proxy.image" -}}
{{- $registry := .Values.global.imageRegistry | default .Values.image.registry }}
{{- $repository := .Values.image.repository }}
{{- $tag := .Values.image.tag | default .Chart.AppVersion }}
{{- if $registry }}
{{- printf "%s/%s:%s" $registry $repository $tag }}
{{- else }}
{{- printf "%s:%s" $repository $tag }}
{{- end }}
{{- end }}

{{/*
Create pod labels
*/}}
{{- define "registry-proxy.podLabels" -}}
{{ include "registry-proxy.selectorLabels" . }}
{{- with .Values.pod.labels }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Create tolerations
*/}}
{{- define "registry-proxy.tolerations" -}}
{{- with .Values.tolerations }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Create node selector
*/}}
{{- define "registry-proxy.nodeSelector" -}}
{{- with .Values.nodeSelector }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Create affinity
*/}}
{{- define "registry-proxy.affinity" -}}
{{- with .Values.affinity }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Create ConfigMap name
*/}}
{{- define "registry-proxy.configMapName" -}}
{{- if .Values.configMap.name }}
{{- .Values.configMap.name }}
{{- else }}
{{- printf "%s-config" (include "registry-proxy.fullname" .) }}
{{- end }}
{{- end }}
