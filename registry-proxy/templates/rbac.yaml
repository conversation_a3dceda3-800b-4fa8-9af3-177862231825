{{- if .Values.rbac.create -}}
# ClusterRole for registry proxy
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "registry-proxy.fullname" . }}
  labels:
    {{- include "registry-proxy.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - get
  - list
  - watch
---
# ClusterRoleBinding for registry proxy
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "registry-proxy.fullname" . }}
  labels:
    {{- include "registry-proxy.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "registry-proxy.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "registry-proxy.serviceAccountName" . }}
  namespace: {{ include "registry-proxy.namespace" . }}
{{- end }}
