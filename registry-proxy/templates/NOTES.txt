1. Check the registry proxy status:
  kubectl get daemonset {{ include "registry-proxy.fullname" . }} -n {{ include "registry-proxy.namespace" . }}

2. View registry proxy pods:
  kubectl get pods -l "{{ include "registry-proxy.selectorLabels" . | replace ": " "=" | replace "\n" "," }}" -n {{ include "registry-proxy.namespace" . }}

3. Check registry proxy logs:
  kubectl logs -f daemonset/{{ include "registry-proxy.fullname" . }} -n {{ include "registry-proxy.namespace" . }}

4. Verify device plugin registration:
  # Check if device plugins are registered with kubelet
  kubectl get nodes -o json | jq '.items[].status.allocatable'

5. Check device plugin sockets:
  # On a node where the agent is running
  ls -la /var/lib/kubelet/registry-proxys/

7. Check environment variables:
  # View environment variables in running pods
  kubectl exec -it <pod-name> -n {{ include "registry-proxy.namespace" . }} -- env | grep -E "(NODE_NAME|PLUGIN|CONFIG)"

8. Monitor device allocation:
  # Check which pods are using devices
  kubectl describe nodes | grep -A 10 "Allocated resources"

9. Troubleshooting:
  # Check if nodes have the required label
  kubectl get nodes --show-labels | grep gpu.enable

  # Check if pods are scheduled on correct nodes
  kubectl get pods -n {{ include "registry-proxy.namespace" . }} -o wide

  # Check device plugin events
  kubectl get events -n {{ include "registry-proxy.namespace" . }} --sort-by=.metadata.creationTimestamp

Registry Proxy Configuration:
- Target nodes: {{ .Values.nodeSelector | toYaml | nindent 2 }}
- Host network: {{ .Values.pod.hostNetwork }}
- Privileged mode: {{ .Values.container.securityContext.privileged }}
- Device plugin path: {{ .Values.volumes.devicePlugin.hostPath }}
- Pod resources path: {{ .Values.volumes.podResources.hostPath }}
{{- if .Values.configMap.enabled }}
- ConfigMap enabled: {{ .Values.configMap.enabled }}
- ConfigMap name: {{ include "registry-proxy.configMapName" . }}
- Config mount path: {{ .Values.volumes.config.mountPath }}
{{- else }}
- ConfigMap enabled: {{ .Values.configMap.enabled }}
{{- end }}

For more information about device plugins, visit:
https://kubernetes.io/docs/concepts/extend-kubernetes/compute-storage-net/registry-proxys/
