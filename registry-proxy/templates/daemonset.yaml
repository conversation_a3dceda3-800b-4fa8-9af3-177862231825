apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ include "registry-proxy.fullname" . }}
  namespace: {{ include "registry-proxy.namespace" . }}
  labels:
    {{- include "registry-proxy.labels" . | nindent 4 }}
spec:
  revisionHistoryLimit: {{ .Values.daemonset.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "registry-proxy.selectorLabels" . | nindent 6 }}
  {{- with .Values.daemonset.updateStrategy }}
  updateStrategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      {{- with .Values.pod.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "registry-proxy.podLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "registry-proxy.serviceAccountName" . }}
      {{- if .Values.pod.hostNetwork }}
      hostNetwork: {{ .Values.pod.hostNetwork }}
      {{- end }}
      {{- if .Values.pod.dnsPolicy }}
      dnsPolicy: {{ .Values.pod.dnsPolicy }}
      {{- end }}
      {{- with .Values.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      containers:
      - name: {{ .Values.container.name }}
        image: {{ include "registry-proxy.image" . }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- with .Values.container.securityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- if .Values.container.args }}
        args:
        {{- range .Values.container.args }}
        - {{ . | quote }}
        {{- end }}
        {{- end }}
        {{- if or .Values.container.env .Values.extraEnv }}
        env:
        {{- if .Values.container.env }}
        {{- range .Values.container.env }}
        - {{ toYaml . | nindent 10 | trim }}
        {{- end }}
        {{- end }}
        {{- if and .Values.extraEnv (gt (len .Values.extraEnv) 0) }}
        {{- range .Values.extraEnv }}
        - {{ toYaml . | nindent 10 | trim }}
        {{- end }}
        {{- end }}
        {{- end }}
        {{- if .Values.container.ports }}
        ports:
        {{- range .Values.container.ports }}
        - {{ toYaml . | nindent 10 | trim }}
        {{- end }}
        {{- end }}
        {{- if .Values.container.resources }}
        resources:
          {{- toYaml .Values.container.resources | nindent 10 }}
        {{- end }}
        {{- if .Values.configMap.enabled }}
        volumeMounts:
        - name: config
          mountPath: {{ .Values.volumes.config.mountPath }}
          readOnly: true
        {{- end }}
        {{- if .Values.healthcheck.enabled }}
        {{- with .Values.healthcheck.livenessProbe }}
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f registry-proxy"
          initialDelaySeconds: {{ .initialDelaySeconds }}
          periodSeconds: {{ .periodSeconds }}
          timeoutSeconds: {{ .timeoutSeconds }}
          failureThreshold: {{ .failureThreshold }}
        {{- end }}
        {{- with .Values.healthcheck.readinessProbe }}
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f registry-proxy"
          initialDelaySeconds: {{ .initialDelaySeconds }}
          periodSeconds: {{ .periodSeconds }}
          timeoutSeconds: {{ .timeoutSeconds }}
          failureThreshold: {{ .failureThreshold }}
        {{- end }}
        {{- end }}
       {{- if .Values.configMap.enabled }}
      volumes:
      - name: config
        configMap:
          name: {{ include "registry-proxy.configMapName" . }}
          defaultMode: 0644
      {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector:
        {{- include "registry-proxy.nodeSelector" . | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations:
        {{- include "registry-proxy.tolerations" . | nindent 8 }}
      {{- end }}
      {{- if .Values.affinity }}
      affinity:
        {{- include "registry-proxy.affinity" . | nindent 8 }}
      {{- end }}
