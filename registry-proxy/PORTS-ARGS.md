# Registry Proxy Ports and Arguments Configuration Guide

## Overview

The Registry Proxy Helm chart supports configurable container ports and arguments, allowing you to customize how the registry proxy service listens and operates.

## Default Configuration

By default, the chart configures the registry proxy with:

```yaml
container:
  ports:
    - containerPort: 5000
      hostPort: 5000
  
  args:
    - --listen-addr=:5000
```

## Configuration Options

### Container Ports

| Parameter | Description | Default |
|-----------|-------------|---------|
| `container.ports` | Array of port configurations | `[{"containerPort": 5000, "hostPort": 5000}]` |
| `container.ports[].containerPort` | Port inside the container | `5000` |
| `container.ports[].hostPort` | Port on the host node | `5000` |
| `container.ports[].protocol` | Protocol (TCP/UDP) | `TCP` (default) |
| `container.ports[].name` | Port name for identification | Optional |

### Container Arguments

| Parameter | Description | Default |
|-----------|-------------|---------|
| `container.args` | Array of command-line arguments | `["--listen-addr=:5000"]` |

## Usage Examples

### 1. Basic Port Customization

```bash
# Change the default port to 8080
helm install registry-proxy ./registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --set container.ports[0].containerPort=8080 \
  --set container.ports[0].hostPort=8080 \
  --set-string 'container.args[0]=--listen-addr=:8080'
```

### 2. Multiple Ports Configuration

```yaml
# values-multi-ports.yaml
container:
  ports:
    # Main service port
    - containerPort: 8080
      hostPort: 8080
      protocol: TCP
      name: http
    
    # Metrics port
    - containerPort: 9090
      hostPort: 9090
      protocol: TCP
      name: metrics
    
    # Health check port
    - containerPort: 8081
      hostPort: 8081
      protocol: TCP
      name: health

  args:
    - --listen-addr=:8080
    - --metrics-addr=:9090
    - --health-addr=:8081
```

Install with:
```bash
helm install registry-proxy ./registry-proxy \
  --namespace torin-system \
  --create-namespace \
  -f values-multi-ports.yaml
```

### 3. Advanced Arguments Configuration

```yaml
# values-advanced-args.yaml
container:
  args:
    # Service configuration
    - --listen-addr=:8080
    - --metrics-addr=:9090
    - --health-addr=:8081
    
    # Logging configuration
    - --log-level=info
    - --log-format=json
    
    # Registry configuration
    - --registry-url=https://harbor.intra.moments8.com
    - --registry-timeout=30s
    
    # Cache configuration
    - --cache-size=1GB
    - --cache-ttl=24h
    
    # TLS configuration
    - --tls-cert=/etc/certs/tls.crt
    - --tls-key=/etc/certs/tls.key
    
    # Performance tuning
    - --max-connections=1000
    - --read-timeout=30s
    - --write-timeout=30s
```

### 4. Environment-Specific Configuration

#### Development Environment
```yaml
# values-dev.yaml
container:
  ports:
    - containerPort: 5000
      hostPort: 5000
  
  args:
    - --listen-addr=:5000
    - --log-level=debug
    - --log-format=text
    - --cache-size=100MB
```

#### Production Environment
```yaml
# values-prod.yaml
container:
  ports:
    - containerPort: 8080
      hostPort: 8080
      name: http
    - containerPort: 9090
      hostPort: 9090
      name: metrics
  
  args:
    - --listen-addr=:8080
    - --metrics-addr=:9090
    - --log-level=info
    - --log-format=json
    - --cache-size=2GB
    - --max-connections=5000
```

## Common Use Cases

### 1. Registry Pull-Through Cache

```yaml
container:
  ports:
    - containerPort: 5000
      hostPort: 5000
  
  args:
    - --listen-addr=:5000
    - --mode=pull-through
    - --upstream-registry=docker.io
    - --cache-size=10GB
    - --cache-ttl=168h  # 7 days
```

### 2. Registry Mirror

```yaml
container:
  ports:
    - containerPort: 5000
      hostPort: 5000
  
  args:
    - --listen-addr=:5000
    - --mode=mirror
    - --mirror-registry=harbor.intra.moments8.com
    - --sync-interval=1h
```

### 3. Registry with Authentication

```yaml
container:
  ports:
    - containerPort: 5000
      hostPort: 5000
  
  args:
    - --listen-addr=:5000
    - --auth-enabled=true
    - --auth-realm=Registry Realm
    - --auth-service=registry-proxy
```

## Verification

After deployment, verify the configuration:

```bash
# Check pod configuration
kubectl get pods -n torin-system -l app=registry-proxy -o yaml | grep -A 10 "ports:\|args:"

# Check port accessibility
curl -f http://<node-ip>:5000/v2/ || echo "Registry not accessible"

# Check logs for startup arguments
kubectl logs -n torin-system -l app=registry-proxy | grep "Starting with args"

# Test registry functionality
docker pull <node-ip>:5000/library/hello-world
```

## Troubleshooting

### Port Binding Issues

1. **Port already in use**:
   ```bash
   # Check what's using the port
   netstat -tulpn | grep :5000
   
   # Use a different port
   --set container.ports[0].hostPort=5001
   ```

2. **Permission denied**:
   ```bash
   # Use ports > 1024 for non-privileged containers
   --set container.ports[0].containerPort=8080
   --set container.ports[0].hostPort=8080
   ```

### Argument Issues

1. **Invalid arguments**:
   ```bash
   # Check container logs for argument errors
   kubectl logs -n torin-system -l app=registry-proxy
   ```

2. **Configuration conflicts**:
   ```bash
   # Validate argument combinations
   helm template registry-proxy ./registry-proxy | grep -A 20 args:
   ```

## Best Practices

1. **Use non-privileged ports** (> 1024) when possible
2. **Enable metrics** for monitoring and observability
3. **Configure health checks** for proper load balancing
4. **Use TLS** in production environments
5. **Set appropriate cache sizes** based on usage patterns
6. **Monitor resource usage** and adjust limits accordingly

## Integration with Other Services

### Prometheus Monitoring

```yaml
container:
  ports:
    - containerPort: 5000
      hostPort: 5000
      name: http
    - containerPort: 9090
      hostPort: 9090
      name: metrics
  
  args:
    - --listen-addr=:5000
    - --metrics-addr=:9090
    - --metrics-path=/metrics

pod:
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
```

### Load Balancer Integration

```yaml
container:
  ports:
    - containerPort: 8080
      hostPort: 8080
      name: http
    - containerPort: 8081
      hostPort: 8081
      name: health
  
  args:
    - --listen-addr=:8080
    - --health-addr=:8081
    - --health-path=/health

healthcheck:
  enabled: true
  readinessProbe:
    httpGet:
      path: /health
      port: 8081
```

This configuration provides maximum flexibility for deploying registry proxy in various environments and use cases.
