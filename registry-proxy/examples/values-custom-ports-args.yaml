# Example values for registry-proxy with custom ports and arguments
# This shows how to customize container ports and arguments

# Use specific image version
image:
  tag: "v1.0.0"
  pullPolicy: IfNotPresent

# Container configuration with custom ports and arguments
container:
  # Custom port configuration
  ports:
    # Main registry proxy port
    - containerPort: 8080
      hostPort: 8080
      protocol: TCP
    
    # Metrics port (optional)
    - containerPort: 9090
      hostPort: 9090
      protocol: TCP
      name: metrics
    
    # Health check port (optional)
    - containerPort: 8081
      hostPort: 8081
      protocol: TCP
      name: health

  # Custom arguments for the registry proxy
  args:
    # Listen address for the main service
    - --listen-addr=:8080
    
    # Metrics server configuration
    - --metrics-addr=:9090
    
    # Health check endpoint
    - --health-addr=:8081
    
    # Log level configuration
    - --log-level=info
    
    # Log format (json or text)
    - --log-format=json
    
    # Registry configuration
    - --registry-url=https://harbor.intra.moments8.com
    
    # Cache configuration
    - --cache-size=1GB
    - --cache-ttl=24h
    
    # TLS configuration (if needed)
    # - --tls-cert=/etc/certs/tls.crt
    # - --tls-key=/etc/certs/tls.key
    
    # Configuration file path (if using ConfigMap)
    # - --config-file=/etc/registry-proxy/config.yaml

  # Enhanced resource allocation for custom configuration
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 512Mi

  # Additional environment variables for custom configuration
  env:
    # Node name (default)
    - name: NODE_NAME
      valueFrom:
        fieldRef:
          fieldPath: spec.nodeName
    
    # Custom environment variables
    - name: REGISTRY_PROXY_PORT
      value: "8080"
    
    - name: METRICS_PORT
      value: "9090"
    
    - name: HEALTH_PORT
      value: "8081"

# Additional environment variables
extraEnv:
  # Registry credentials (from secret)
  - name: REGISTRY_ROBOT_USERNAME
    valueFrom:
      secretKeyRef:
        name: registry-robot-credentials
        key: username
  
  - name: REGISTRY_ROBOT_PASSWORD
    valueFrom:
      secretKeyRef:
        name: registry-robot-credentials
        key: password
  
  # Custom configuration
  - name: PROXY_MODE
    value: "pull-through"
  
  - name: CACHE_ENABLED
    value: "true"
  
  - name: DEBUG_MODE
    value: "false"

# Enable health checks for custom ports
healthcheck:
  enabled: true
  livenessProbe:
    # Use HTTP probe for health check port
    httpGet:
      path: /health
      port: 8081
    initialDelaySeconds: 30
    periodSeconds: 30
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    # Use HTTP probe for readiness check
    httpGet:
      path: /ready
      port: 8081
    initialDelaySeconds: 5
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

# Node selector for specific nodes
nodeSelector:
  # Target nodes with registry proxy enabled
  registry-proxy.enable: "true"
  # Also target GPU nodes if needed
  gpu.enable: "true"

# Enhanced tolerations for registry proxy
tolerations:
  - key: CriticalAddonsOnly
    operator: Exists
  - effect: NoSchedule
    key: node-role.kubernetes.io/master
    operator: Exists
  - effect: NoSchedule
    key: node-role.kubernetes.io/control-plane
    operator: Exists
  - effect: NoSchedule
    key: node.kubernetes.io/not-ready
    operator: Exists
  # Custom toleration for registry proxy nodes
  - key: registry-proxy
    operator: Equal
    value: "true"
    effect: NoSchedule

# Pod annotations for monitoring and configuration
pod:
  annotations:
    # Prometheus scraping configuration
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
    prometheus.io/path: "/metrics"
    
    # Custom annotations
    registry-proxy.moments8.com/version: "v1.0.0"
    registry-proxy.moments8.com/config: "custom"

# Priority class for important system component
priorityClassName: "system-node-critical"
