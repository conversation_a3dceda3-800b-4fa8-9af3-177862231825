# Default values for registry-proxy
# This is a YAML-formatted file.

# Global configuration values
global:
  # Override image registry for all images (leave empty to use chart defaults)
  imageRegistry: ""

# Override the chart name (leave empty to use chart name)
nameOverride: ""
# Override the full resource name (leave empty to auto-generate)
fullnameOverride: ""

# Container image configuration
image:
  # Container registry URL
  registry: "harbor.intra.moments8.com"
  # Image repository path
  repository: moments8/torin/registry-proxy
  # Image pull policy (Always, IfNotPresent, Never)
  pullPolicy: Always
  # Image tag (overrides the image tag whose default is the chart appVersion)
  tag: ""

# Image pull secrets for private registries
imagePullSecrets: []

# DaemonSet configuration
daemonset:
  # Revision history limit for the DaemonSet
  revisionHistoryLimit: 5
  
  # Update strategy for the DaemonSet
  updateStrategy:
    # Rolling update type for gradual updates
    type: RollingUpdate
    rollingUpdate:
      # Maximum number of pods that can be unavailable during update
      maxUnavailable: 1

# Pod configuration
pod:
  # Pod annotations
  annotations: {}
  
  # Pod labels (additional to the default ones)
  labels: {}
  
  # Use host network for device plugin communication
  hostNetwork: true
  
  # DNS policy when using host network
  dnsPolicy: ClusterFirstWithHostNet

# Container configuration
container:
  # Container name
  name: registry-proxy

  # Security context for the container
  securityContext:
    # Run with privileged access (required for device plugin)
    privileged: true

  # Environment variables for the container
  env:
    # Node name where the pod is running
    - name: NODE_NAME
      valueFrom:
        fieldRef:
          fieldPath: spec.nodeName

  # Resource allocation for the container
  resources:
    # Minimum resources guaranteed to the container
    requests:
      # Minimum CPU allocation (50 millicores)
      cpu: 50m
      # Minimum memory allocation (64MB)
      memory: 64Mi
    # Maximum resources the container can use
    limits:
      # Maximum CPU limit (200 millicores)
      cpu: 200m
      # Maximum memory limit (128MB)
      memory: 128Mi

# Volume configuration
volumes:
  # Device plugin socket directory
  devicePlugin:
    # Host path for device plugin sockets
    hostPath: /var/lib/kubelet/registry-proxys
    # Mount path in container
    mountPath: /var/lib/kubelet/registry-proxys

  # Pod resources directory
  podResources:
    # Host path for pod resources (note: different from kubelet default)
    hostPath: /data/lib/kubelet/pod-resources
    # Mount path in container
    mountPath: /var/lib/kubelet/pod-resources

  # ConfigMap mount configuration
  config:
    # Mount path for configuration files
    mountPath: /etc/device-weight-config

# Node selection and scheduling
nodeSelector:
  # Target nodes with GPU enabled (default selector)
  gpu.enable: "true"

# Tolerations for pod assignment to nodes
tolerations:
  # Tolerate critical addons
  - key: CriticalAddonsOnly
    operator: Exists
  # Tolerate master/control-plane nodes
  - effect: NoSchedule
    key: node-role.kubernetes.io/master
    operator: Exists
  - effect: NoSchedule
    key: node-role.kubernetes.io/control-plane
    operator: Exists
  # Tolerate not-ready nodes
  - effect: NoSchedule
    key: node.kubernetes.io/not-ready
    operator: Exists

# Affinity rules for pod assignment (optional)
affinity: {}

# Priority class for the pods
priorityClassName: ""

# Additional environment variables for the container
# These will be merged with container.env
# Example:
# extraEnv:
#   - name: CUSTOM_VAR
#     value: "custom_value"
#   - name: SECRET_VAR
#     valueFrom:
#       secretKeyRef:
#         name: my-secret
#         key: secret-key
extraEnv:
  - name: REGISTRY_ROBOT_USERNAME
    valueFrom:
      secretKeyRef:
        name: registry-robot-credentials
        key: username
  - name: REGISTRY_ROBOT_PASSWORD
    valueFrom:
      secretKeyRef:
        name: registry-robot-credentials
        key: password

# Service account configuration
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use (if not set and create is true, a name is generated)
  name: ""

# RBAC configuration
rbac:
  # Create RBAC resources
  create: true

# ConfigMap configuration for device weight mapping
configMap:
  # Enable ConfigMap creation
  enabled: false

# Additional labels applied to all resources
additionalLabels:
  app.kubernetes.io/name: registry-proxy
  app.kubernetes.io/part-of: torin-system

# Health checks configuration
healthcheck:
  # Enable health checks (device plugins typically don't expose health endpoints)
  enabled: false
  
  # Liveness probe configuration
  livenessProbe:
    # Initial delay before first probe
    initialDelaySeconds: 30
    # How often to perform the probe
    periodSeconds: 30
    # Timeout for each probe
    timeoutSeconds: 5
    # Number of failures before restarting
    failureThreshold: 3
  
  # Readiness probe configuration  
  readinessProbe:
    # Initial delay before first probe
    initialDelaySeconds: 5
    # How often to perform the probe
    periodSeconds: 10
    # Timeout for each probe
    timeoutSeconds: 5
    # Number of failures before marking as not ready
    failureThreshold: 3
