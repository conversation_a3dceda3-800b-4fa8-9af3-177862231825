# Helm generated files
*.tgz
*.tar.gz

# Helm charts directory (when using helm dependency)
# charts/
Chart.lock

# Helm release artifacts
*.rendered

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment specific values (optional - uncomment if needed)
# values-local.yaml
# values-dev.yaml
# values-staging.yaml
# values-prod.yaml
# values-*.local.yaml

# Secrets and sensitive files
secrets.yaml
*.secret.yaml
*.secrets.yaml
.env
.env.local
.env.*.local

# Backup files
*.bak
*.backup
*.orig

# Log files
*.log
logs/

# Test output
test-output/
*.test

# Coverage reports
coverage/
*.coverage

# Node modules (if using any npm tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python cache (if using any Python tools)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Go binaries (if using any Go tools)
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out

# Helm plugin cache
.helm/

# Custom ignore patterns
# Add your project-specific ignores below this line