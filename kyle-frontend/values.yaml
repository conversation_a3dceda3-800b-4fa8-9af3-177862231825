# Default values for kyle-frontend
# This is a YAML-formatted file.

# Global configuration values that can be shared across charts
global:
  # Override image registry for all images (leave empty to use chart defaults)
  imageRegistry: ""
  # Deployment environment (dev, test, staging, prod)
  environment: prod
  # Whether this is a Spring Boot application (false for frontend applications)
  springBoot: false

# Override the chart name (leave empty to use chart name)
nameOverride: ""
# Override the full resource name (leave empty to auto-generate)
fullnameOverride: ""

# Container image configuration
image:
  # Container registry URL
  registry: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com"
  # Image repository path for main frontend
  repository: moments8/kyle-frontend
  # Image pull policy (Always, IfNotPresent, Never)
  pullPolicy: Always
  # Image tag (use specific version in production instead of 'latest')
  tag: latest

# Number of pod replicas to run
replicaCount: 1

# Deployment configuration for frontend application
deployment:
  # Enable Deployment (frontend applications are typically stateless)
  enabled: true
  # Update strategy for zero-downtime deployments
  updateStrategy:
    # Rolling update type for gradual replacement of pods
    type: RollingUpdate
    rollingUpdate:
      # Maximum number of pods that can be unavailable during update
      maxUnavailable: 0
      # Maximum number of pods that can be created above desired replica count
      maxSurge: 1

# Pod lifecycle hooks for graceful shutdown
lifecycle:
  # Pre-stop hook to gracefully shutdown nginx
  preStop:
    exec:
      # Wait 30 seconds for connections to drain, then gracefully stop nginx
      command: ["/bin/sh", "-c", "sleep 30 && nginx -s quit"]

# Environment variables from parent chart
envFromParentChart:
  # Disable inheritance (frontend typically doesn't need backend environment variables)
  enabled: false

# ConfigMap configuration
configMap:
  # Disable ConfigMap (frontend configuration usually handled via build-time or environment)
  enabled: false

# Kubernetes Service configuration
service:
  # Service type (ClusterIP for internal access)
  type: ClusterIP
  # Service port exposed to other services and ingress
  port: 80
  # Container port that the service forwards to (nginx default)
  targetPort: 80
  # Metrics endpoint configuration
  metrics:
    # Disable metrics (frontend applications typically don't expose metrics)
    enabled: false

# Resource allocation for the frontend container
resources:
  # Minimum resources guaranteed to the container
  requests:
    # Minimum memory allocation (128MB for nginx-based frontend)
    memory: "128Mi"
    # Minimum CPU allocation (0.1 cores)
    cpu: "100m"
  # Maximum resources the container can use
  limits:
    # Maximum memory limit (256MB for static content serving)
    memory: "256Mi"
    # Maximum CPU limit (0.5 cores)
    cpu: "500m"

# Health check configuration for Kubernetes probes
healthcheck:
  # Startup probe - not needed for nginx (starts quickly)
  startup:
    # Disable startup probe for frontend applications
    enabled: false

  # Liveness probe - checks if nginx is running properly
  liveness:
    # Enable liveness probe
    enabled: true
    # Health check endpoint path (custom health endpoint)
    path: /health
    # Port to check
    port: 80
    # Delay before first probe after container start
    initialDelaySeconds: 0
    # How often to perform the probe (in seconds)
    periodSeconds: 10
    # Timeout for each probe attempt
    timeoutSeconds: 10
    # Number of consecutive failures before restarting container
    failureThreshold: 3

  # Readiness probe - checks if frontend is ready to serve traffic
  readiness:
    # Enable readiness probe
    enabled: true
    # Health check endpoint path
    path: /health
    # Port to check
    port: 80
    # Delay before first probe after container start (short for frontend)
    initialDelaySeconds: 3
    # How often to perform the probe (in seconds)
    periodSeconds: 10
    # Number of consecutive successes required to mark as ready
    successThreshold: 1
    # Timeout for each probe attempt
    timeoutSeconds: 10
    # Number of consecutive failures before removing from service endpoints
    failureThreshold: 3

# Ingress configuration for external access to main frontend
ingress:
  # Enable ingress to expose the frontend externally
  enabled: true
  # Ingress class (alb for AWS Application Load Balancer)
  className: alb
  # Ingress annotations for load balancer configuration
  annotations:
    # Configure ALB to listen on both HTTP and HTTPS ports
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80},{"HTTPS": 443}]'
    # Automatically redirect HTTP traffic to HTTPS
    alb.ingress.kubernetes.io/ssl-redirect: 'true'
  # Hostname for the ingress (leave empty to use auto-generated host)
  host: ""
  # Path-based routing configuration
  paths:
    # Route all requests (/) to this frontend service (main application entry point)
    - path: /
      # Path matching type (Prefix matches all paths)
      pathType: Prefix

# Additional labels applied to all resources
additionalLabels:
  # System/project this application belongs to
  app.kubernetes.io/part-of: kyle-system