#!/bin/bash

# Script to compare original deploy.yaml with helm-generated YAML
# Focus on differences other than labels and annotations

set -e

echo "🔍 Comparing Inference Operator deploy.yaml vs Helm-generated YAML"
echo "================================================================="

# Generate helm template
helm template inference-operator ./inference-operator --namespace torin-system --include-crds > /tmp/helm-generated.yaml

echo "📋 1. Image differences:"
echo "Original deploy.yaml:"
grep -A 1 "image:" inference-operator/deploy.yaml | grep -v "^--"
echo ""
echo "Helm-generated:"
grep -A 1 "image:" /tmp/helm-generated.yaml | grep -v "^--"
echo ""

echo "📋 2. Strategy differences:"
echo "Original deploy.yaml:"
grep -A 5 "strategy:" inference-operator/deploy.yaml || echo "No strategy defined"
echo ""
echo "Helm-generated:"
grep -A 5 "strategy:" /tmp/helm-generated.yaml || echo "No strategy defined"
echo ""

echo "📋 3. Ports differences:"
echo "Original deploy.yaml:"
grep -A 10 "ports:" inference-operator/deploy.yaml || echo "No ports defined in containers"
echo ""
echo "Helm-generated:"
grep -A 10 "ports:" /tmp/helm-generated.yaml | head -15
echo ""

echo "📋 4. ImagePullPolicy differences:"
echo "Original deploy.yaml:"
grep "imagePullPolicy:" inference-operator/deploy.yaml || echo "No imagePullPolicy defined"
echo ""
echo "Helm-generated:"
grep "imagePullPolicy:" /tmp/helm-generated.yaml || echo "No imagePullPolicy defined"
echo ""

echo "📋 5. Resource count comparison:"
echo "Original deploy.yaml resource counts:"
echo "  ServiceAccount: $(grep -c "kind: ServiceAccount" inference-operator/deploy.yaml)"
echo "  Role: $(grep -c "kind: Role" inference-operator/deploy.yaml)"
echo "  ClusterRole: $(grep -c "kind: ClusterRole" inference-operator/deploy.yaml)"
echo "  RoleBinding: $(grep -c "kind: RoleBinding" inference-operator/deploy.yaml)"
echo "  ClusterRoleBinding: $(grep -c "kind: ClusterRoleBinding" inference-operator/deploy.yaml)"
echo "  Service: $(grep -c "kind: Service" inference-operator/deploy.yaml)"
echo "  Deployment: $(grep -c "kind: Deployment" inference-operator/deploy.yaml)"
echo "  CustomResourceDefinition: $(grep -c "kind: CustomResourceDefinition" inference-operator/deploy.yaml)"
echo ""
echo "Helm-generated resource counts:"
echo "  ServiceAccount: $(grep -c "kind: ServiceAccount" /tmp/helm-generated.yaml)"
echo "  Role: $(grep -c "kind: Role" /tmp/helm-generated.yaml)"
echo "  ClusterRole: $(grep -c "kind: ClusterRole" /tmp/helm-generated.yaml)"
echo "  RoleBinding: $(grep -c "kind: RoleBinding" /tmp/helm-generated.yaml)"
echo "  ClusterRoleBinding: $(grep -c "kind: ClusterRoleBinding" /tmp/helm-generated.yaml)"
echo "  Service: $(grep -c "kind: Service" /tmp/helm-generated.yaml)"
echo "  Deployment: $(grep -c "kind: Deployment" /tmp/helm-generated.yaml)"
echo "  CustomResourceDefinition: $(grep -c "kind: CustomResourceDefinition" /tmp/helm-generated.yaml)"
echo ""

echo "📋 6. Missing resources in Helm template:"
echo "Checking for CRD in default template (should be 0):"
grep -c "kind: CustomResourceDefinition" /tmp/helm-generated.yaml || echo "0 (CRDs not included by default)"
echo ""

echo "📋 7. Container specification differences:"
echo "Original deploy.yaml container spec (key fields):"
grep -A 20 "containers:" inference-operator/deploy.yaml | grep -E "(name:|image:|command:|args:)" | head -10
echo ""
echo "Helm-generated container spec (key fields):"
grep -A 20 "containers:" /tmp/helm-generated.yaml | grep -E "(name:|image:|command:|args:)" | head -10
echo ""

echo "📋 8. Service selector differences:"
echo "Original deploy.yaml service selector:"
grep -A 5 "selector:" inference-operator/deploy.yaml | tail -3
echo ""
echo "Helm-generated service selector:"
grep -A 5 "selector:" /tmp/helm-generated.yaml | tail -3
echo ""

echo "📋 9. Pod template differences:"
echo "Original deploy.yaml pod template labels:"
grep -A 10 "template:" inference-operator/deploy.yaml | grep -A 5 "labels:" | head -5
echo ""
echo "Helm-generated pod template labels:"
grep -A 10 "template:" /tmp/helm-generated.yaml | grep -A 5 "labels:" | head -5
echo ""

# Clean up
rm -f /tmp/helm-generated.yaml

echo "✅ Comparison complete!"
