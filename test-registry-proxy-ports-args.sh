#!/bin/bash

# Test script for registry-proxy ports and args feature
# This script validates the ports and arguments functionality

set -e

CHART_PATH="registry-proxy"
RELEASE_NAME="test-registry-proxy"
NAMESPACE="torin-system"

echo "🔧 Testing Registry Proxy Ports and Args Feature"
echo "==============================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Step 1: Checking prerequisites"
if ! command_exists helm; then
    echo "❌ Helm is not installed"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Test default ports and args
echo "📋 Step 2: Testing default ports and args"
helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" > /tmp/default-ports-args-template.yaml

# Check if default port is present
if ! grep -q "containerPort: 5000" /tmp/default-ports-args-template.yaml; then
    echo "❌ Default containerPort 5000 not found"
    exit 1
fi

if ! grep -q "hostPort: 5000" /tmp/default-ports-args-template.yaml; then
    echo "❌ Default hostPort 5000 not found"
    exit 1
fi

# Check if default args is present
if ! grep -q "\-\-listen-addr=:5000" /tmp/default-ports-args-template.yaml; then
    echo "❌ Default args --listen-addr=:5000 not found"
    exit 1
fi

echo "✅ Default ports and args test passed"

# Test custom ports
echo "📋 Step 3: Testing custom ports"
helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    --set container.ports[0].containerPort=8080 \
    --set container.ports[0].hostPort=8080 > /tmp/custom-ports-template.yaml

# Check if custom port is present
if ! grep -q "containerPort: 8080" /tmp/custom-ports-template.yaml; then
    echo "❌ Custom containerPort 8080 not found"
    exit 1
fi

if ! grep -q "hostPort: 8080" /tmp/custom-ports-template.yaml; then
    echo "❌ Custom hostPort 8080 not found"
    exit 1
fi

echo "✅ Custom ports test passed"

# Test multiple ports
echo "📋 Step 4: Testing multiple ports"
cat > /tmp/multi-ports-values.yaml << 'EOF'
container:
  ports:
    - containerPort: 8080
      hostPort: 8080
      protocol: TCP
    - containerPort: 9090
      hostPort: 9090
      protocol: TCP
      name: metrics
EOF

helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    -f /tmp/multi-ports-values.yaml > /tmp/multi-ports-template.yaml

# Check if both ports are present
if ! grep -q "containerPort: 8080" /tmp/multi-ports-template.yaml; then
    echo "❌ First port 8080 not found"
    exit 1
fi

if ! grep -q "containerPort: 9090" /tmp/multi-ports-template.yaml; then
    echo "❌ Second port 9090 not found"
    exit 1
fi

if ! grep -q "name: metrics" /tmp/multi-ports-template.yaml; then
    echo "❌ Port name 'metrics' not found"
    exit 1
fi

echo "✅ Multiple ports test passed"

# Test custom args
echo "📋 Step 5: Testing custom args"
helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    --set-string 'container.args[0]=--listen-addr=:8080' \
    --set-string 'container.args[1]=--log-level=debug' > /tmp/custom-args-template.yaml

# Check if custom args are present
if ! grep -q "\-\-listen-addr=:8080" /tmp/custom-args-template.yaml; then
    echo "❌ Custom args --listen-addr=:8080 not found"
    exit 1
fi

if ! grep -q "\-\-log-level=debug" /tmp/custom-args-template.yaml; then
    echo "❌ Custom args --log-level=debug not found"
    exit 1
fi

echo "✅ Custom args test passed"

# Test complex args
echo "📋 Step 6: Testing complex args"
cat > /tmp/complex-args-values.yaml << 'EOF'
container:
  args:
    - --listen-addr=:8080
    - --metrics-addr=:9090
    - --log-level=info
    - --log-format=json
    - --registry-url=https://harbor.intra.moments8.com
    - --cache-size=1GB
EOF

helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    -f /tmp/complex-args-values.yaml > /tmp/complex-args-template.yaml

# Check if complex args are present
if ! grep -q "\-\-metrics-addr=:9090" /tmp/complex-args-template.yaml; then
    echo "❌ Complex args --metrics-addr=:9090 not found"
    exit 1
fi

if ! grep -q "\-\-registry-url=https://harbor.intra.moments8.com" /tmp/complex-args-template.yaml; then
    echo "❌ Complex args --registry-url not found"
    exit 1
fi

if ! grep -q "\-\-cache-size=1GB" /tmp/complex-args-template.yaml; then
    echo "❌ Complex args --cache-size=1GB not found"
    exit 1
fi

echo "✅ Complex args test passed"

# Test custom example configuration
echo "📋 Step 7: Testing custom example configuration"
if [ -f "$CHART_PATH/examples/values-custom-ports-args.yaml" ]; then
    helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
        -f "$CHART_PATH/examples/values-custom-ports-args.yaml" > /tmp/example-config-template.yaml
    
    # Check if example configuration is applied
    if ! grep -q "containerPort: 8080" /tmp/example-config-template.yaml; then
        echo "❌ Example config port 8080 not found"
        exit 1
    fi
    
    if ! grep -q "\-\-metrics-addr=:9090" /tmp/example-config-template.yaml; then
        echo "❌ Example config metrics args not found"
        exit 1
    fi
    
    echo "✅ Custom example configuration test passed"
else
    echo "⚠️  Custom example configuration not found, skipping"
fi

# Test empty ports and args
echo "📋 Step 8: Testing empty ports and args"
helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    --set 'container.ports=null' \
    --set 'container.args=null' > /tmp/empty-ports-args-template.yaml

# Should not have ports or args sections when null
if grep -q "ports:" /tmp/empty-ports-args-template.yaml; then
    echo "❌ Ports section found when it should be null"
    exit 1
fi

if grep -q "args:" /tmp/empty-ports-args-template.yaml; then
    echo "❌ Args section found when it should be null"
    exit 1
fi

echo "✅ Empty ports and args test passed"

# Test YAML structure
echo "📋 Step 9: Testing YAML structure"
# Extract container section and verify structure
grep -A 50 "containers:" /tmp/default-ports-args-template.yaml > /tmp/container-section.yaml

# Check if args comes before env
ARGS_LINE=$(grep -n "args:" /tmp/container-section.yaml | cut -d: -f1 || echo "0")
ENV_LINE=$(grep -n "env:" /tmp/container-section.yaml | cut -d: -f1 || echo "999")

if [ "$ARGS_LINE" -gt "$ENV_LINE" ] && [ "$ARGS_LINE" -ne "0" ]; then
    echo "❌ Args should come before env section"
    exit 1
fi

# Check if ports comes after env
PORTS_LINE=$(grep -n "ports:" /tmp/container-section.yaml | cut -d: -f1 || echo "0")

if [ "$PORTS_LINE" -lt "$ENV_LINE" ] && [ "$PORTS_LINE" -ne "0" ]; then
    echo "❌ Ports should come after env section"
    exit 1
fi

echo "✅ YAML structure test passed"

# Clean up temporary files
echo "📋 Step 10: Cleaning up"
rm -f /tmp/default-ports-args-template.yaml
rm -f /tmp/custom-ports-template.yaml
rm -f /tmp/multi-ports-template.yaml
rm -f /tmp/custom-args-template.yaml
rm -f /tmp/complex-args-template.yaml
rm -f /tmp/example-config-template.yaml
rm -f /tmp/empty-ports-args-template.yaml
rm -f /tmp/multi-ports-values.yaml
rm -f /tmp/complex-args-values.yaml
rm -f /tmp/container-section.yaml

echo ""
echo "🎉 All ports and args tests passed!"
echo ""
echo "📊 Test Summary:"
echo "   ✅ Default ports and args"
echo "   ✅ Custom ports configuration"
echo "   ✅ Multiple ports support"
echo "   ✅ Custom args configuration"
echo "   ✅ Complex args support"
echo "   ✅ Custom example configuration"
echo "   ✅ Empty ports and args handling"
echo "   ✅ YAML structure validation"
echo ""
echo "📋 Ports and Args Features:"
echo "   • Default port 5000 with hostPort binding"
echo "   • Default args --listen-addr=:5000"
echo "   • Support for multiple ports with names"
echo "   • Support for complex argument lists"
echo "   • Proper YAML structure (args before env, ports after env)"
echo "   • Null/empty handling"
echo ""
echo "📋 Usage Examples:"
echo "   # Install with default ports and args"
echo "   helm install registry-proxy $CHART_PATH --namespace $NAMESPACE --create-namespace"
echo ""
echo "   # Customize port"
echo "   helm install registry-proxy $CHART_PATH --namespace $NAMESPACE --create-namespace \\"
echo "     --set container.ports[0].containerPort=8080 --set container.ports[0].hostPort=8080"
echo ""
echo "   # Customize args"
echo "   helm install registry-proxy $CHART_PATH --namespace $NAMESPACE --create-namespace \\"
echo "     --set-string 'container.args[0]=--listen-addr=:8080'"
echo ""
echo "   # Use custom configuration"
echo "   helm install registry-proxy $CHART_PATH --namespace $NAMESPACE --create-namespace \\"
echo "     -f $CHART_PATH/examples/values-custom-ports-args.yaml"
