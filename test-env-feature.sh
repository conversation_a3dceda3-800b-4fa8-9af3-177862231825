#!/bin/bash

# Test script for device-plugin-agent environment variables feature
# This script validates the environment variable functionality

set -e

CHART_PATH="device-plugin-agent"
RELEASE_NAME="test-env"
NAMESPACE="torin-system"

echo "🔧 Testing Device Plugin Agent Environment Variables Feature"
echo "=========================================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Step 1: Checking prerequisites"
if ! command_exists helm; then
    echo "❌ Helm is not installed"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Test default environment variables
echo "📋 Step 2: Testing default environment variables"
helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" > /tmp/default-env-template.yaml

# Check if default NODE_NAME environment variable is present
if ! grep -q "NODE_NAME" /tmp/default-env-template.yaml; then
    echo "❌ Default NODE_NAME environment variable not found"
    exit 1
fi

# Check if fieldRef is correctly configured
if ! grep -A 3 "NODE_NAME" /tmp/default-env-template.yaml | grep -q "spec.nodeName"; then
    echo "❌ NODE_NAME fieldRef not correctly configured"
    exit 1
fi

echo "✅ Default environment variables test passed"

# Test additional environment variables via extraEnv
echo "📋 Step 3: Testing additional environment variables"
helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    --set-string 'extraEnv[0].name=CUSTOM_VAR,extraEnv[0].value=test123' \
    --set-string 'extraEnv[1].name=LOG_LEVEL,extraEnv[1].value=debug' > /tmp/extra-env-template.yaml

# Check if custom environment variables are present
if ! grep -q "CUSTOM_VAR" /tmp/extra-env-template.yaml; then
    echo "❌ Custom environment variable CUSTOM_VAR not found"
    exit 1
fi

if ! grep -q "LOG_LEVEL" /tmp/extra-env-template.yaml; then
    echo "❌ Custom environment variable LOG_LEVEL not found"
    exit 1
fi

# Check if values are correctly set
if ! grep -A 1 "CUSTOM_VAR" /tmp/extra-env-template.yaml | grep -q "test123"; then
    echo "❌ CUSTOM_VAR value not correctly set"
    exit 1
fi

echo "✅ Additional environment variables test passed"

# Test environment variables from fieldRef
echo "📋 Step 4: Testing fieldRef environment variables"
cat > /tmp/fieldref-values.yaml << 'EOF'
extraEnv:
  - name: POD_NAME
    valueFrom:
      fieldRef:
        fieldPath: metadata.name
  - name: POD_NAMESPACE
    valueFrom:
      fieldRef:
        fieldPath: metadata.namespace
  - name: POD_IP
    valueFrom:
      fieldRef:
        fieldPath: status.podIP
EOF

helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    -f /tmp/fieldref-values.yaml > /tmp/fieldref-env-template.yaml

# Check if fieldRef environment variables are present
if ! grep -q "POD_NAME" /tmp/fieldref-env-template.yaml; then
    echo "❌ POD_NAME fieldRef environment variable not found"
    exit 1
fi

if ! grep -q "metadata.name" /tmp/fieldref-env-template.yaml; then
    echo "❌ POD_NAME fieldRef not correctly configured"
    exit 1
fi

echo "✅ FieldRef environment variables test passed"

# Test environment variables from resourceFieldRef
echo "📋 Step 5: Testing resourceFieldRef environment variables"
cat > /tmp/resourceref-values.yaml << 'EOF'
extraEnv:
  - name: MEMORY_LIMIT
    valueFrom:
      resourceFieldRef:
        resource: limits.memory
        divisor: "1Mi"
  - name: CPU_LIMIT
    valueFrom:
      resourceFieldRef:
        resource: limits.cpu
        divisor: "1m"
EOF

helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    -f /tmp/resourceref-values.yaml > /tmp/resourceref-env-template.yaml

# Check if resourceFieldRef environment variables are present
if ! grep -q "MEMORY_LIMIT" /tmp/resourceref-env-template.yaml; then
    echo "❌ MEMORY_LIMIT resourceFieldRef environment variable not found"
    exit 1
fi

if ! grep -q "limits.memory" /tmp/resourceref-env-template.yaml; then
    echo "❌ MEMORY_LIMIT resourceFieldRef not correctly configured"
    exit 1
fi

echo "✅ ResourceFieldRef environment variables test passed"

# Test environment variables with custom config example
echo "📋 Step 6: Testing with custom config example"
if [ -f "$CHART_PATH/examples/values-with-env.yaml" ]; then
    helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
        -f "$CHART_PATH/examples/values-with-env.yaml" > /tmp/custom-env-template.yaml
    
    # Check if example environment variables are present
    if ! grep -q "PLUGIN_NAME" /tmp/custom-env-template.yaml; then
        echo "❌ PLUGIN_NAME from custom config not found"
        exit 1
    fi
    
    if ! grep -q "CONFIG_PATH" /tmp/custom-env-template.yaml; then
        echo "❌ CONFIG_PATH from custom config not found"
        exit 1
    fi
    
    echo "✅ Custom config example test passed"
else
    echo "⚠️  Custom config example not found, skipping"
fi

# Test environment variables order (container.env should come before extraEnv)
echo "📋 Step 7: Testing environment variables order"
helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    --set-string 'extraEnv[0].name=EXTRA_VAR,extraEnv[0].value=extra' > /tmp/order-env-template.yaml

# Extract environment variables section
grep -A 20 "env:" /tmp/order-env-template.yaml > /tmp/env-section.yaml

# Check if NODE_NAME comes before EXTRA_VAR
NODE_NAME_LINE=$(grep -n "NODE_NAME" /tmp/env-section.yaml | cut -d: -f1)
EXTRA_VAR_LINE=$(grep -n "EXTRA_VAR" /tmp/env-section.yaml | cut -d: -f1)

if [ "$NODE_NAME_LINE" -gt "$EXTRA_VAR_LINE" ]; then
    echo "❌ Environment variables order is incorrect (container.env should come before extraEnv)"
    exit 1
fi

echo "✅ Environment variables order test passed"

# Test empty extraEnv
echo "📋 Step 8: Testing empty extraEnv"
helm template "$RELEASE_NAME" "$CHART_PATH" --namespace "$NAMESPACE" \
    --set 'extraEnv=null' > /tmp/empty-extra-env-template.yaml

# Should still have NODE_NAME but no extra variables
if ! grep -q "NODE_NAME" /tmp/empty-extra-env-template.yaml; then
    echo "❌ NODE_NAME missing when extraEnv is empty"
    exit 1
fi

echo "✅ Empty extraEnv test passed"

# Clean up temporary files
echo "📋 Step 9: Cleaning up"
rm -f /tmp/default-env-template.yaml
rm -f /tmp/extra-env-template.yaml
rm -f /tmp/fieldref-env-template.yaml
rm -f /tmp/resourceref-env-template.yaml
rm -f /tmp/custom-env-template.yaml
rm -f /tmp/order-env-template.yaml
rm -f /tmp/empty-extra-env-template.yaml
rm -f /tmp/fieldref-values.yaml
rm -f /tmp/resourceref-values.yaml
rm -f /tmp/env-section.yaml

echo ""
echo "🎉 All environment variables tests passed!"
echo ""
echo "📊 Test Summary:"
echo "   ✅ Default environment variables (NODE_NAME)"
echo "   ✅ Additional environment variables (extraEnv)"
echo "   ✅ FieldRef environment variables"
echo "   ✅ ResourceFieldRef environment variables"
echo "   ✅ Custom config example"
echo "   ✅ Environment variables order"
echo "   ✅ Empty extraEnv handling"
echo ""
echo "📋 Environment Variables Features:"
echo "   • Default NODE_NAME from fieldRef"
echo "   • Support for extraEnv array"
echo "   • FieldRef support (pod metadata)"
echo "   • ResourceFieldRef support (resource limits)"
echo "   • SecretKeyRef support (via extraEnv)"
echo "   • ConfigMapKeyRef support (via extraEnv)"
echo "   • Proper ordering (container.env before extraEnv)"
echo ""
echo "📋 Usage Examples:"
echo "   # Install with default environment variables"
echo "   helm install device-plugin-agent $CHART_PATH --namespace $NAMESPACE --create-namespace"
echo ""
echo "   # Add custom environment variables"
echo "   helm install device-plugin-agent $CHART_PATH --namespace $NAMESPACE --create-namespace \\"
echo "     --set-string 'extraEnv[0].name=LOG_LEVEL,extraEnv[0].value=debug'"
echo ""
echo "   # Use custom environment config"
echo "   helm install device-plugin-agent $CHART_PATH --namespace $NAMESPACE --create-namespace \\"
echo "     -f $CHART_PATH/examples/values-with-env.yaml"
