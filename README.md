# Moments8 Helm Charts

This repository contains Helm charts for deploying Moments8 applications on Kubernetes.

## Library
- [common](common): A chart library includes all the basic templates for common components. It is used by other charts.

## Charts

- [maas-system](maas-system/Chart.yaml): A Helm chart for deploying the Moments8 MaaS system, including PostgreSQL, Redis, and other dependencies.
- [maas-frontend](maas-frontend/Chart.yaml): A Helm chart for deploying the Moments8 MaaS frontend.
- [maas-backend](maas-backend/Chart.yaml): A Helm chart for deploying the Moments8 MaaS backend.
- [kyle-backend](kyle-backend/Chart.yaml): A Helm chart for deploying the Moments8 Kyle backend.
- [maas-admin-backend](maas-admin-backend/Chart.yaml): A Helm chart for deploying the Moments8 MaaS admin backend.
- [maas-admin-frontend](maas-admin-frontend/Chart.yaml): A Helm chart for deploying the Moments8 MaaS admin frontend.
- [image-warmup-operator](image-warmup-operator/Chart.yaml): A Helm chart for deploying the Moments8 Image Warmup Operator, a Kubernetes operator that pre-pulls container images on nodes to improve pod startup times.

## Usage

To use these charts, you will need to have Helm installed on your Kubernetes cluster. You can then install the charts using the `helm install` command.

```bash
helm upgrade --install <release-name> oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts/<chart-name> --namespace <namespace>
```

## Development

### Add new chart

```bash
helm create <chart-name>
```

### Deploy chart to ACR

```bash
cd <chart-name>
find . -name "*.tgz" | xargs rm -f # remove old package
helm dependency build # build dependencies if needed
helm package ./
helm push <chart-name>.tgz oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts
```
