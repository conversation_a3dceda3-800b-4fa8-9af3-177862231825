# Moments8 Helm Charts

This repository contains Helm charts for deploying Moments8 applications and infrastructure components on Kubernetes.

## Library
- [common](common): A chart library that includes all the basic templates for common components. It is used by other charts as a dependency.

## Application Charts

### MaaS Platform
- [maas-system](maas-system): A Helm chart for deploying the Moments8 MaaS system, including PostgreSQL, Redis, and other dependencies.
- [maas-frontend](maas-frontend): A Helm chart for deploying the Moments8 MaaS frontend.
- [maas-backend](maas-backend): A Helm chart for deploying the Moments8 MaaS backend.
- [maas-admin-frontend](maas-admin-frontend): A Helm chart for deploying the Moments8 MaaS admin frontend.
- [maas-admin-backend](maas-admin-backend): A Helm chart for deploying the Moments8 MaaS admin backend.

### Kyle Platform
- [kyle-backend](kyle-backend): A Helm chart for deploying the Moments8 Kyle backend.

## Infrastructure Charts

### Operators
- [image-warmup-operator](image-warmup-operator): A Kubernetes operator that pre-pulls container images on nodes to improve pod startup times and reduce cold start latency.
- [maas-operator](maas-operator): A Kubernetes operator for managing MAAS (Model as a Service) services and inference workloads with automated deployment and lifecycle management.
- [inference-operator](inference-operator): A Kubernetes operator for managing inference services and GPU workloads with flexible resource allocation and scaling capabilities.

### System Components
- [device-plugin-agent](device-plugin-agent): A Kubernetes device plugin for GPU and hardware resource management, enabling efficient allocation and scheduling of specialized hardware resources.
- [registry-proxy](registry-proxy): A container registry proxy service that provides caching and acceleration for container image pulls in Kubernetes clusters.

## Usage

To use these charts, you will need to have Helm installed on your Kubernetes cluster. You can then install the charts using the `helm install` command.

### From OCI Registry

* ARC: `oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts`
* Harbor: `oci://harbor.intra.moments8.com/helm-charts`

```bash
# Install from ARC registry
helm upgrade --install <release-name> oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts/<chart-name> \
  --namespace <namespace> \
  --create-namespace \
  --version <chart-version>

# Install from Harbor registry
helm upgrade --install <release-name> oci://harbor.intra.moments8.com/helm-charts/<chart-name> \
  --namespace <namespace> \
  --create-namespace \
  --version <chart-version>
```

### From Local Repository

```bash
# Clone the repository
git clone https://bitbucket.intra.moments8.com/scm/mm8/helm-charts.git
cd helm-charts

# Install from local directory
helm upgrade --install <release-name> ./<chart-name> \
  --namespace <namespace> \
  --create-namespace
```

## Development

### Add New Chart

```bash
# Create a new chart with Helm's starter template
helm create <chart-name>

# Or copy an existing chart as a starting point
cp -r device-plugin-agent <new-chart-name>
```

### Test Chart

```bash
# Lint the chart
helm lint <chart-name>

# Render templates locally
helm template <release-name> ./<chart-name> --namespace <namespace>

# Perform a dry-run installation
helm install <release-name> ./<chart-name> --namespace <namespace> --dry-run
```

### Package and Deploy Chart

```bash
cd <chart-name>
# Remove old packages
find . -name "*.tgz" | xargs rm -f

# Build dependencies if needed
helm dependency build

# Package the chart
helm package ./

# Push to Harbor registry
helm push <chart-name>-<version>.tgz oci://harbor.intra.moments8.com/helm-charts
```

### Version Management

- Update the `version` field in `Chart.yaml` when making changes
- Follow semantic versioning (MAJOR.MINOR.PATCH)
- Document changes in a CHANGELOG.md file

## Chart Details

### Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- Access to Harbor registry: `harbor.intra.moments8.com`

### Common Configuration

Most charts support the following common configuration patterns:

```yaml
# Image configuration
image:
  registry: harbor.intra.moments8.com
  repository: moments8/torin/<component>
  tag: "latest"
  pullPolicy: Always

# Resource allocation
resources:
  requests:
    cpu: 100m
    memory: 128Mi
  limits:
    cpu: 500m
    memory: 256Mi

# Node scheduling
nodeSelector:
  torin-components-scheduling: enable

tolerations:
  - key: CriticalAddonsOnly
    operator: Exists
```

### Installation Examples

#### Application Charts

##### MaaS System
```bash
helm install maas-system ./maas-system \
  --namespace maas \
  --create-namespace \
  --set global.postgresql.auth.password=<password> \
  --set global.redis.auth.password=<password>
```

##### MaaS Frontend
```bash
helm install maas-frontend ./maas-frontend \
  --namespace maas \
  --set ingress.enabled=true \
  --set ingress.hosts[0].host=maas.example.com
```

##### MaaS Backend
```bash
helm install maas-backend ./maas-backend \
  --namespace maas \
  --set maasSystem.url=http://maas-system-service.maas.svc.cluster.local
```

##### Kyle Backend
```bash
helm install kyle-backend ./kyle-backend \
  --namespace kyle \
  --create-namespace \
  --set global.postgresql.auth.password=<password>
```

#### Infrastructure Charts

##### Image Warmup Operator
```bash
helm install image-warmup-operator ./image-warmup-operator \
  --namespace torin-system \
  --create-namespace
```

##### MAAS Operator
```bash
helm install maas-operator ./maas-operator \
  --namespace torin-system \
  --create-namespace \
  --set image.tag=v1.0.7
```

##### Inference Operator
```bash
helm install inference-operator ./inference-operator \
  --namespace torin-system \
  --create-namespace \
  --set image.tag=v1.0.0
```

##### Device Plugin Agent
```bash
helm install device-plugin-agent ./device-plugin-agent \
  --namespace torin-system \
  --create-namespace \
  --set nodeSelector."gpu\.enable"=true
```

##### Registry Proxy
```bash
helm install registry-proxy ./registry-proxy \
  --namespace torin-system \
  --create-namespace \
  --set container.ports[0].hostPort=5000
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test the chart thoroughly
5. Submit a pull request
