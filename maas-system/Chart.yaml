apiVersion: v2
name: maas-system
version: 0.1.4
dependencies:
  - name: redis
    version: "21.2.3"  # 指定版本号
    repository: "file://./charts/redis"
    condition: redis.enabled
  - name: postgresql
    version: "16.7.10"   # 指定版本号
    repository: "file://./charts/postgresql"
    condition: postgresql.enabled
  - name: maas-frontend
    version: "0.1.2"
    # repository: "file://./charts/maas-frontend"
    repository: "oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts"
    condition: maas-frontend.enabled
  - name: maas-admin-frontend
    version: "0.1.2"
    # repository: "file://./charts/maas-admin-frontend"
    repository: "oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts"
    condition: maas-admin-frontend.enabled
  - name: maas-backend
    version: "0.1.2"
    # repository: "file://./charts/maas-backend"
    repository: "oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts"
    condition: maas-backend.enabled
  - name: maas-admin-backend
    version: "0.1.2"
    # repository: "file://./charts/maas-admin-backend"
    repository: "oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts"
    condition: maas-admin-backend.enabled