{{- $postgresMaasPassword := include "maas.postgresql.maasPassword" . }}
{{- $postgresAdminPassword := include "maas.postgresql.adminPassword" . }}
{{- $postgresCronjobPassword := include "maas.postgresql.cronjobPassword" . }}
{{- $postgresMaasUsername := include "maas.postgresql.maasUsername" . }}
{{- $postgresAdminUsername := include "maas.postgresql.adminUsername" . }}
{{- $postgresCronjobUsername := include "maas.postgresql.cronjobUsername" . }}
{{- $redisPassword := include "maas.redis.password" . | b64enc | quote }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-auth
  labels: {{- include "maas.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-5"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  postgres-admin-password: {{ include "maas.postgresql.postgresPassword" . | b64enc | quote }}
  postgres-database: {{ include "maas.postgresql.database" . | b64enc | quote }}
  redis-password: {{ $redisPassword }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-db-init-scripts
  labels: {{- include "maas.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-5"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
stringData:
  01-init-users.sql: |
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE USER {{ $postgresMaasUsername }} WITH PASSWORD '{{ $postgresMaasPassword }}';
    CREATE USER {{ $postgresAdminUsername }} WITH PASSWORD '{{ $postgresAdminPassword }}';
    CREATE USER {{ $postgresCronjobUsername }} WITH PASSWORD '{{ $postgresCronjobPassword }}';
    CREATE SCHEMA mas AUTHORIZATION {{ $postgresMaasUsername }};
    CREATE SCHEMA admin AUTHORIZATION {{ $postgresAdminUsername }};
    GRANT USAGE ON SCHEMA mas TO {{ $postgresAdminUsername }};
    GRANT USAGE ON SCHEMA admin TO {{ $postgresMaasUsername }};
    GRANT USAGE, CREATE ON SCHEMA mas TO {{ $postgresCronjobUsername }};
    GRANT USAGE, CREATE ON SCHEMA admin TO {{ $postgresCronjobUsername }};
    GRANT SELECT ON ALL TABLES IN SCHEMA mas TO {{ $postgresAdminUsername }};
    GRANT SELECT ON ALL TABLES IN SCHEMA admin TO {{ $postgresMaasUsername }};
    GRANT ALL ON ALL TABLES IN SCHEMA mas TO {{ $postgresCronjobUsername }};
    GRANT ALL ON ALL TABLES IN SCHEMA admin TO {{ $postgresCronjobUsername }};
    ALTER DEFAULT PRIVILEGES FOR ROLE {{ $postgresMaasUsername }} IN SCHEMA mas GRANT SELECT ON TABLES TO {{ $postgresAdminUsername }};
    ALTER DEFAULT PRIVILEGES FOR ROLE {{ $postgresMaasUsername }} IN SCHEMA mas GRANT ALL ON TABLES TO {{ $postgresCronjobUsername }};
    ALTER DEFAULT PRIVILEGES FOR ROLE {{ $postgresAdminUsername }} IN SCHEMA admin GRANT SELECT ON TABLES TO {{ $postgresMaasUsername }};
    ALTER DEFAULT PRIVILEGES FOR ROLE {{ $postgresAdminUsername }} IN SCHEMA admin GRANT ALL ON TABLES TO {{ $postgresCronjobUsername }};
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-db-maas-auth
  labels: {{- include "maas.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-5"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  DB_PASSWORD: {{ $postgresMaasPassword | b64enc | quote }}
  REDIS_PASSWORD: {{ $redisPassword }}
  DB_USERNAME: {{ $postgresMaasUsername | b64enc | quote }}
  REDIS_SENTINEL_NODES: {{ include "maas.redis.sentinelAddresses" . | b64enc | quote }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-db-admin-auth
  labels: {{- include "maas.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-5"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  DB_PASSWORD: {{ $postgresAdminPassword | b64enc | quote }}
  REDIS_PASSWORD: {{ $redisPassword }}
  DB_USERNAME: {{ $postgresAdminUsername | b64enc | quote }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-db-cronjob-auth
  labels: {{- include "maas.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-5"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  DB_PASSWORD: {{ $postgresCronjobPassword | b64enc | quote }}
  DB_USERNAME: {{ $postgresCronjobUsername  | b64enc | quote }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-email-auth
  labels: {{- include "maas.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-5"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  MAIL_HOST: {{ include "maas.email.host" . | b64enc | quote }}
  MAIL_PORT: {{ include "maas.email.port" . | b64enc | quote }}
  MAIL_USERNAME:  {{ include "maas.email.username" . | b64enc | quote }}
  MAIL_PASSWORD: {{ include "maas.email.password" . | quote }}
