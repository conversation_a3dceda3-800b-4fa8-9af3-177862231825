{{- if .Values.global.jwtKeysAutoGen }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-jwt-keygen
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-1"
    "helm.sh/hook-delete-policy":  before-hook-creation,hook-succeeded,hook-failed
spec:
  template:
    spec:
      containers:
      - name: jwt-keygen
        image: moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/standard/maven:3.9.9-eclipse-temurin-23-with-kubectl
        command:
        - bash
        - -c
        - |
          # 设置错误处理
          set -e

          # 检查并删除已存在的密钥
          if kubectl get secret {{ .Release.Name }}-jwt-keys -n {{ .Release.Namespace }} 2>/dev/null; then
              echo "JWT keys already exist, skipping generation"
              exit 0
          fi

          # 重试机制的密钥生成函数
          generate_keys() {
              local attempt=1
              local max_attempts=3
              
              while [ $attempt -le $max_attempts ]; do
                  echo "Attempting to generate keys (attempt $attempt/$max_attempts)..."
                  
                  # 创建Java密钥生成器
                  cat > RSAKeyGen.java << 'EOF'
          import java.security.KeyPair;
          import java.security.KeyPairGenerator;
          import java.security.NoSuchAlgorithmException;
          import java.security.SecureRandom;
          import java.security.interfaces.RSAPrivateKey;
          import java.security.interfaces.RSAPublicKey;
          import java.util.Base64;

          public class RSAKeyGen {
              private static final String KEY_ALGORITHM = "RSA";
              private static final int KEY_SIZE = 2048;

              public static void main(String[] args) {
                  try {
                      // 使用SecureRandom确保随机性
                      SecureRandom secureRandom = new SecureRandom();
                      
                      // 初始化密钥对生成器
                      KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
                      keyPairGen.initialize(KEY_SIZE, secureRandom);

                      // 生成密钥对
                      KeyPair keyPair = keyPairGen.generateKeyPair();

                      // 获取公钥和私钥
                      RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
                      RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();

                      // 验证密钥不为空
                      if (publicKey == null || privateKey == null) {
                          System.err.println("Generated keys are null");
                          System.exit(1);
                      }

                      // 转换为Base64编码
                      String publicKeyBase64 = Base64.getEncoder().encodeToString(publicKey.getEncoded());
                      String privateKeyBase64 = Base64.getEncoder().encodeToString(privateKey.getEncoded());

                      // 验证编码后的密钥不为空
                      if (publicKeyBase64.isEmpty() || privateKeyBase64.isEmpty()) {
                          System.err.println("Encoded keys are empty");
                          System.exit(1);
                      }

                      // 输出密钥供shell脚本使用
                      System.out.println("PUBLIC_KEY=" + publicKeyBase64);
                      System.out.println("PRIVATE_KEY=" + privateKeyBase64);
                      
                  } catch (NoSuchAlgorithmException e) {
                      System.err.println("Error generating RSA key: " + e.getMessage());
                      System.exit(1);
                  } catch (Exception e) {
                      System.err.println("Unexpected error: " + e.getMessage());
                      System.exit(1);
                  }
              }
          }
          EOF
                  
                  # 编译Java程序
                  if ! javac RSAKeyGen.java; then
                      echo "Failed to compile Java program"
                      exit 1
                  fi
                  
                  # 运行Java程序生成密钥
                  if java RSAKeyGen > keys.env 2>&1; then
                      # 检查生成的密钥文件
                      if [ -s keys.env ]; then
                          # 读取并验证密钥
                          source keys.env
                          
                          # 验证密钥变量不为空
                          if [ -n "$PUBLIC_KEY" ] && [ -n "$PRIVATE_KEY" ]; then
                              echo "Keys generated successfully on attempt $attempt"
                              return 0
                          else
                              echo "Generated keys are empty on attempt $attempt"
                          fi
                      else
                          echo "Keys file is empty on attempt $attempt"
                      fi
                  else
                      echo "Failed to run Java program on attempt $attempt"
                      cat keys.env 2>/dev/null || true
                  fi
                  
                  # 清理失败的尝试
                  rm -f keys.env
                  
                  attempt=$((attempt + 1))
                  if [ $attempt -le $max_attempts ]; then
                      echo "Retrying in 2 seconds..."
                      sleep 2
                  fi
              done
              
              echo "Failed to generate valid keys after $max_attempts attempts"
              exit 1
          }

          # 生成密钥
          generate_keys

          # 验证密钥内容
          echo "Validating generated keys..."
          if [ ${#PUBLIC_KEY} -lt 100 ] || [ ${#PRIVATE_KEY} -lt 100 ]; then
              echo "Error: Generated keys appear to be too short"
              echo "Public key length: ${#PUBLIC_KEY}"
              echo "Private key length: ${#PRIVATE_KEY}"
              exit 1
          fi

          # 创建Kubernetes Secret
          echo "Creating Kubernetes secret..."
          if kubectl create secret generic {{ .Release.Name }}-jwt-keys \
              --from-literal=JWT_PUBLIC_KEY="$PUBLIC_KEY" \
              --from-literal=JWT_PRIVATE_KEY="$PRIVATE_KEY" \
              -n {{ .Release.Namespace }}; then

              echo "Secret created, now adding labels and annotations..."
              
              # 添加labels
              kubectl label secret {{ .Release.Name }}-jwt-keys \
                  app.kubernetes.io/instance={{ .Release.Name }} \
                  app.kubernetes.io/managed-by=Helm \
                  app.kubernetes.io/name={{ .Chart.Name }} \
                  -n {{ .Release.Namespace }}
              
              # 添加annotations
              kubectl annotate secret {{ .Release.Name }}-jwt-keys \
                  meta.helm.sh/release-name={{ .Release.Name }} \
                  meta.helm.sh/release-namespace={{ .Release.Namespace }} \
                  -n {{ .Release.Namespace }}

              echo "JWT keys generated and stored successfully"
              
              # 验证创建的Secret
              echo "Verifying created secret..."
              if kubectl get secret {{ .Release.Name }}-jwt-keys -n {{ .Release.Namespace }} -o jsonpath='{.data.JWT_PUBLIC_KEY}' | base64 -d | wc -c | grep -q -v '^0$'; then
                  echo "Secret verification passed"
              else
                  echo "Warning: Secret verification failed - keys may be empty"
                  exit 1
              fi
          else
              echo "Failed to create Kubernetes secret"
              exit 1
          fi
      restartPolicy: OnFailure
      serviceAccountName: {{ .Release.Name }}-jwt-keygen
---
# ServiceAccount for the job
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Release.Name }}-jwt-keygen
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-2"
    "helm.sh/hook-delete-policy":  before-hook-creation,hook-succeeded,hook-failed
---
# ClusterRole for the job
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ .Release.Name }}-jwt-keygen
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-2" 
    "helm.sh/hook-delete-policy":  before-hook-creation,hook-succeeded,hook-failed
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "create", "update", "patch"]
---
# RoleBinding for the job
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ .Release.Name }}-jwt-keygen
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "-2"
    "helm.sh/hook-delete-policy":  before-hook-creation,hook-succeeded,hook-failed
subjects:
- kind: ServiceAccount
  name: {{ .Release.Name }}-jwt-keygen
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: Role
  name: {{ .Release.Name }}-jwt-keygen
  apiGroup: rbac.authorization.k8s.io
{{- end }}