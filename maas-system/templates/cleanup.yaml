{{- if .Values.global.pvcCleanup.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "maas.fullname" . }}-cleanup-pvcs
  labels:
    {{- include "maas.labels" . | nindent 4 }}
    app.kubernetes.io/component: cleanup
  annotations:
    helm.sh/hook: pre-install
    helm.sh/hook-weight: "-1"
    helm.sh/hook-delete-policy:  before-hook-creation,hook-succeeded,hook-failed
spec:
  template:
    metadata:
      labels:
        {{- include "maas.labels" . | nindent 8 }}
        app.kubernetes.io/component: cleanup
    spec:
      serviceAccountName: {{ include "maas.fullname" . }}-cleanup-sa
      containers:
      - name: cleanup
        image: moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/standard/maven:3.9.9-eclipse-temurin-23-with-kubectl
        imagePullPolicy: IfNotPresent
        command:
        - /bin/sh
        - -c
        - |
          set -e
          echo "Starting PVC cleanup for release: {{ .Release.Name }}"
          
          # 根据配置的选择器来删除 PVC
          SELECTOR="app.kubernetes.io/instance={{ .Release.Name }}"

          echo "Using selector: $SELECTOR"
          
          # 列出匹配的 PVC
          echo "Found PVCs to delete:"
          kubectl get pvc -l "$SELECTOR" --no-headers 2>/dev/null || echo "No PVCs found matching selector"
          
          # 删除匹配的 PVC
          if kubectl get pvc -l "$SELECTOR" --no-headers 2>/dev/null | grep -q .; then
            echo "Deleting PVCs..."
            # 建议添加更好的错误处理
            if ! kubectl delete pvc -l "$SELECTOR" --timeout=30s; then
              echo "Warning: Some PVCs may not have been deleted completely"
              exit 0
            fi
            echo "PVC cleanup completed successfully"
          else
            echo "No PVCs to delete"
          fi
      restartPolicy: Never
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "maas.fullname" . }}-cleanup-role
  labels:
    {{- include "maas.labels" . | nindent 4 }}
    app.kubernetes.io/component: cleanup
  annotations:
    helm.sh/hook: pre-install
    helm.sh/hook-weight: "-2"
    helm.sh/hook-delete-policy:  before-hook-creation,hook-succeeded,hook-failed
rules:
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["list", "get", "delete"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "maas.fullname" . }}-cleanup-binding
  labels:
    {{- include "maas.labels" . | nindent 4 }}
    app.kubernetes.io/component: cleanup
  annotations:
    helm.sh/hook: pre-install
    helm.sh/hook-weight: "-2"
    helm.sh/hook-delete-policy:  before-hook-creation,hook-succeeded,hook-failed
subjects:
- kind: ServiceAccount
  name: {{ include "maas.fullname" . }}-cleanup-sa
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: Role
  name: {{ include "maas.fullname" . }}-cleanup-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "maas.fullname" . }}-cleanup-sa
  labels:
    {{- include "maas.labels" . | nindent 4 }}
    app.kubernetes.io/component: cleanup
  annotations:
    helm.sh/hook: pre-install
    helm.sh/hook-weight: "-2"
    helm.sh/hook-delete-policy:  before-hook-creation,hook-succeeded,hook-failed
{{- end }}