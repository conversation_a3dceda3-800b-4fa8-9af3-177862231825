{{/*
Expand the name of the chart.
*/}}
{{- define "maas.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
MaaS labels
*/}}
{{- define "maas.labels" -}}
app.kubernetes.io/name: {{ include "maas.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "maas.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Generate PostgreSQL maasPassword
*/}}
{{- define "maas.postgresql.maasPassword" -}}
{{- if .Values.global.postgresql.maasPassword -}}
{{- .Values.global.postgresql.maasPassword -}}
{{- else -}}
{{- randAlphaNum 16 -}}
{{- end -}}
{{- end -}}

{{/*
Generate PostgreSQL adminPassword
*/}}
{{- define "maas.postgresql.adminPassword" -}}
{{- if .Values.global.postgresql.adminPassword -}}
{{- .Values.global.postgresql.adminPassword -}}
{{- else -}}
{{- randAlphaNum 16 -}}
{{- end -}}
{{- end -}}

{{/*
Generate PostgreSQL cronjobPassword
*/}}
{{- define "maas.postgresql.cronjobPassword" -}}
{{- if .Values.global.postgresql.cronjobPassword -}}
{{- .Values.global.postgresql.cronjobPassword -}}
{{- else -}}
{{- randAlphaNum 16 -}}
{{- end -}}
{{- end -}}

{{/*
Generate PostgreSQL maasUsername
*/}}
{{- define "maas.postgresql.maasUsername" -}}
{{- .Values.global.postgresql.maasUsername | default "maas_user" -}}
{{- end -}}

{{/*
Generate PostgreSQL adminUsername
*/}}
{{- define "maas.postgresql.adminUsername" -}}
{{- .Values.global.postgresql.adminUsername | default "admin_user" -}}
{{- end -}}

{{/*
Generate PostgreSQL cronjobUsername
*/}}
{{- define "maas.postgresql.cronjobUsername" -}}
{{- .Values.global.postgresql.cronjobUsername | default "cronjob_user" -}}
{{- end -}}


{{/*
Generate PostgreSQL admin password
*/}}
{{- define "maas.postgresql.postgresPassword" -}}
{{- if .Values.global.postgresql.postgresPassword -}}
{{- .Values.global.postgresql.postgresPassword -}}
{{- else -}}
{{- randAlphaNum 16 -}}
{{- end -}}
{{- end -}}

{{/*
Generate Redis password
*/}}
{{- define "maas.redis.password" -}}
{{- if .Values.redis.auth.password -}}
{{- .Values.redis.auth.password -}}
{{- else -}}
{{- randAlphaNum 16 -}}
{{- end -}}
{{- end -}}

{{/*
Get PostgreSQL database name
*/}}
{{- define "maas.postgresql.database" -}}
{{- .Values.postgresql.auth.database | default "maas" -}}
{{- end -}}

{{/*
Get Redis Sentinel addresses
生成格式: [Release.Name]redis-node[index].[service-name]:26379
*/}}
{{- define "maas.redis.sentinelAddresses" -}}
{{- $addresses := list -}}
{{- $serviceName := printf "%s-redis-headless" .Release.Name -}}
{{- $port := "26379" -}}
{{- range $i := until (.Values.redis.replica.replicaCount | int) -}}
{{- $nodeName := printf "%s-redis-node-%d" $.Release.Name $i -}}
{{- $address := printf "%s.%s:%s" $nodeName $serviceName $port -}}
{{- $addresses = append $addresses $address -}}
{{- end -}}
{{- join "," $addresses -}}
{{- end -}}

{{/*
Get PostgreSQL Hostname
*/}}
{{- define "maas.postgresql.hostname" -}}
{{- printf "%s-postgresql" .Release.Name -}}
{{- end -}}


{{/*
Get maas admin backend url
*/}}
{{- define "maas.admin-backend.url" -}}
{{- printf "http://%s-maas-admin-backend" .Release.Name -}}
{{- end -}}

{{/*
Get maas backend url
*/}}
{{- define "maas.backend.url" -}}
{{- printf "http://%s-maas-backend" .Release.Name -}}
{{- end -}}

{{/*
Get email host, default is "smtpdm.aliyun.com"
*/}}
{{- define "maas.email.host" -}}
{{- .Values.global.email.host | default "smtpdm.aliyun.com" -}}
{{- end -}}

{{/*
Get email port, default is 80
*/}}
{{- define "maas.email.port" -}}
{{- .Values.global.email.port | default 80 -}}
{{- end -}}

{{/*
Get email username, default is "<EMAIL>"
*/}}
{{- define "maas.email.username" -}}
{{- .Values.global.email.username | default "<EMAIL>" -}}
{{- end -}}

{{/*
Get email password, default is "TWFzRGV2MTIzNAo=" which is base64 encoded.
*/}}
{{- define "maas.email.password" -}}
{{- .Values.global.email.password | default "TWFzRGV2MTIzNAo=" -}}
{{- end -}}
