# Global configuration values shared across all components
global:
  # PostgreSQL database configuration
  postgresql:
    # Password for the postgres admin user (leave empty for auto-generation)
    postgresPassword: ""
    # Default database name for the MAAS application
    database: "maas"
    # Username for the main MAAS application database user
    maasUsername: "maas_user"
    # Username for the admin interface database user
    adminUsername: "admin_user"
    # Username for the cronjob service database user
    cronjobUsername: "cronjob_user"
    # Password for the MAAS application user (leave empty for auto-generation)
    maasPassword: ""
    # Password for the admin user (leave empty for auto-generation)
    adminPassword: ""
    # Password for the cronjob user (leave empty for auto-generation)
    cronjobPassword: ""

  # Redis cache configuration
  redis:
    # Redis authentication password (leave empty for auto-generation)
    password: ""

  # Email service configuration for notifications
  email:
    # SMTP server hostname (default: Alibaba Cloud DirectMail)
    host: "smtpdm.aliyun.com"
    # SMTP server port (default: 80 for non-TLS)
    port: 80
    # SMTP username for authentication
    username: "<EMAIL>"
    # SMTP password (base64 encoded, leave empty for auto-generation)
    password: ""

  # Automatically generate JWT signing keys if not provided
  jwtKeysAutoGen: true

  # PVC cleanup configuration
  pvcCleanup:
    # Enable automatic cleanup of PVCs when uninstalling
    enabled: true

# Redis cache service configuration
redis:
  # Enable Redis deployment
  enabled: true
  # Redis architecture: standalone or replication (default: replication for HA)
  architecture: replication

  # Authentication configuration
  auth:
    # Enable Redis authentication
    enabled: true
    # Enable Sentinel authentication
    sentinel: true

  # Redis master node configuration
  master:
    # Persistent storage configuration for master
    persistence:
      # Enable persistent storage for data durability
      enabled: true
      # Storage size for master node
      size: 1Gi
      # Storage class for persistent volumes
      storageClass: moments8-cnfs-nas

    # Resource limits and requests for master node
    resources:
      requests:
        # Minimum memory allocation
        memory: 128Mi
        # Minimum CPU allocation
        cpu: 100m
      limits:
        # Maximum memory limit
        memory: 256Mi
        # Maximum CPU limit
        cpu: 200m

  # Redis replica nodes configuration
  replica:
    # Deployment type for replicas (StatefulSet for persistent storage)
    kind: StatefulSet
    # Number of replica nodes for high availability
    replicaCount: 3

    # Resource limits and requests for replica nodes
    resources:
      requests:
        # Minimum memory allocation
        memory: 128Mi
        # Minimum CPU allocation
        cpu: 100m
      limits:
        # Maximum memory limit
        memory: 256Mi
        # Maximum CPU limit
        cpu: 200m

    # Persistent storage configuration for replicas
    persistence:
      # Enable persistent storage for replicas
      enabled: true
      # Storage size for each replica
      size: 1Gi
      # Storage class for persistent volumes
      storageClass: moments8-cnfs-nas

  # Redis Sentinel configuration for automatic failover
  sentinel:
    # Enable Redis Sentinel for high availability
    enabled: true
    # Master set name for Sentinel monitoring
    masterSet: mymaster

    # Resource limits and requests for Sentinel nodes
    resources:
      requests:
        # Minimum memory allocation (lower than Redis nodes)
        memory: 64Mi
        # Minimum CPU allocation
        cpu: 50m
      limits:
        # Maximum memory limit
        memory: 128Mi
        # Maximum CPU limit
        cpu: 100m

    # Persistent storage configuration for Sentinel
    persistence:
      # Disable persistence for Sentinel (stateless monitoring)
      enabled: false
      # Storage size (not used when disabled)
      size: 1Gi
      # Storage class (not used when disabled)
      storageClass: moments8-cnfs-nas

# PostgreSQL database service configuration
postgresql:
  # Enable PostgreSQL deployment
  enabled: true

  # Authentication configuration
  auth:
    # Enable the default postgres admin user
    enablePostgresUser: true
    # Use password files instead of environment variables for security
    usePasswordFiles: true
    # Default database name (overridden by global.postgresql.database)
    database: "maas"

  # PostgreSQL container image configuration
  image:
    # Private registry for container images
    registry: moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com
    # PostgreSQL image repository
    repository: standard/bitnami/postgresql
    # PostgreSQL version tag
    tag: 17.5.0-debian-12-r10

  # Primary database node configuration
  primary:
    # Resource allocation for the primary database
    resources:
      requests:
        # Minimum CPU allocation (0.5 cores)
        cpu: 500m
        # Minimum memory allocation
        memory: 512Mi
      limits:
        # Maximum CPU allocation (2 cores)
        cpu: 2
        # Maximum memory allocation
        memory: 4Gi

    # Persistent storage configuration
    persistence:
      # Enable persistent storage for database data
      enabled: true
      # Storage class for persistent volumes
      storageClass: moments8-cnfs-nas
      # Storage size for database (10GB)
      size: 10Gi

# MAAS Frontend Application (User Interface)
maas-frontend:
  # Enable the main frontend application
  enabled: true
  image:
    # Container image tag (use specific version in production)
    tag: latest

# MAAS Admin Frontend Application (Admin Interface)
maas-admin-frontend:
  # Enable the admin frontend application
  enabled: true
  image:
    # Container image tag (use specific version in production)
    tag: latest

# MAAS Backend API Service
maas-backend:
  # Enable the main backend API service
  enabled: true
  image:
    # Container image tag (use specific version in production)
    tag: latest

# MAAS Admin Backend API Service
maas-admin-backend:
  # Enable the admin backend API service
  enabled: true
  image:
    # Container image tag (use specific version in production)
    tag: latest