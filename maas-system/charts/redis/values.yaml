# Copyright Broadcom, Inc. All Rights Reserved.
# SPDX-License-Identifier: APACHE-2.0

## @section Global parameters
## Global Docker image parameters
## Please, note that this will override the image parameters, including dependencies, configured to use the global value
## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass
##

## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker registry secret names as an array
## @param global.defaultStorageClass Global default StorageClass for Persistent Volume(s)
## @param global.storageClass DEPRECATED: use global.defaultStorageClass instead
## @param global.redis.password Global Redis(R) password (overrides `auth.password`)
##
global:
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ##
  imagePullSecrets: []
  defaultStorageClass: ""
  storageClass: ""
  ## Security parameters
  ##
  security:
    ## @param global.security.allowInsecureImages Allows skipping image verification
    allowInsecureImages: false
  redis:
    password: ""
  ## Compatibility adaptations for Kubernetes platforms
  ##
  compatibility:
    ## Compatibility adaptations for Openshift
    ##
    openshift:
      ## @param global.compatibility.openshift.adaptSecurityContext Adapt the securityContext sections of the deployment to make them compatible with Openshift restricted-v2 SCC: remove runAsUser, runAsGroup and fsGroup and let the platform use their allowed default IDs. Possible values: auto (apply if the detected running cluster is Openshift), force (perform the adaptation always), disabled (do not perform adaptation)
      ##
      adaptSecurityContext: auto
## @section Common parameters
##

## @param kubeVersion Override Kubernetes version
##
kubeVersion: ""
## @param nameOverride String to partially override common.names.fullname
##
nameOverride: ""
## @param fullnameOverride String to fully override common.names.fullname
##
fullnameOverride: ""
## @param namespaceOverride String to fully override common.names.namespace
##
namespaceOverride: ""
## @param commonLabels Labels to add to all deployed objects
##
commonLabels: {}
## @param commonAnnotations Annotations to add to all deployed objects
##
commonAnnotations: {}
## @param configmapChecksumAnnotations Enable checksum annotations used to trigger rolling updates when ConfigMap(s) change
##
configmapChecksumAnnotations: true
## @param secretChecksumAnnotations Enable checksum annotations used to trigger rolling updates when Secret(s) change
##
secretChecksumAnnotations: true
## @param secretAnnotations Annotations to add to secret
##
secretAnnotations: {}
## @param clusterDomain Kubernetes cluster domain name
##
clusterDomain: cluster.local
## @param extraDeploy Array of extra objects to deploy with the release
##
extraDeploy: []
## @param useHostnames Use hostnames internally when announcing replication. If false, the hostname will be resolved to an IP address
##
useHostnames: true
## @param nameResolutionThreshold Failure threshold for internal hostnames resolution
##
nameResolutionThreshold: 5
## @param nameResolutionTimeout Timeout seconds between probes for internal hostnames resolution
##
nameResolutionTimeout: 5
## Enable diagnostic mode in the deployment
##
diagnosticMode:
  ## @param diagnosticMode.enabled Enable diagnostic mode (all probes will be disabled and the command will be overridden)
  ##
  enabled: false
  ## @param diagnosticMode.command Command to override all containers in the deployment
  ##
  command:
    - sleep
  ## @param diagnosticMode.args Args to override all containers in the deployment
  ##
  args:
    - infinity

## @section Redis(R) Image parameters
##

## Bitnami Redis(R) image
## ref: https://hub.docker.com/r/bitnami/redis/tags/
## @param image.registry [default: REGISTRY_NAME] Redis(R) image registry
## @param image.repository [default: REPOSITORY_NAME/redis] Redis(R) image repository
## @skip image.tag Redis(R) image tag (immutable tags are recommended)
## @param image.digest Redis(R) image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
## @param image.pullPolicy Redis(R) image pull policy
## @param image.pullSecrets Redis(R) image pull secrets
## @param image.debug Enable image debug mode
##
image:
  registry: docker.io
  repository: bitnami/redis
  tag: 8.0.2-debian-12-r3
  digest: ""
  ## Specify a imagePullPolicy
  ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
  ##
  pullPolicy: IfNotPresent
  ## Optionally specify an array of imagePullSecrets.
  ## Secrets must be manually created in the namespace.
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
  ## e.g:
  ## pullSecrets:
  ##   - myRegistryKeySecretName
  ##
  pullSecrets: []
  ## Enable debug mode
  ##
  debug: false
## @section Redis(R) common configuration parameters
## https://github.com/bitnami/containers/tree/main/bitnami/redis#configuration
##

## @param architecture Redis(R) architecture. Allowed values: `standalone` or `replication`
##
architecture: replication
## Redis(R) Authentication parameters
## ref: https://github.com/bitnami/containers/tree/main/bitnami/redis#setting-the-server-password-on-first-run
##
auth:
  ## @param auth.enabled Enable password authentication
  ##
  enabled: true
  ## @param auth.sentinel Enable authentication on sentinels too
  ##
  sentinel: true
  ## @param auth.password Redis(R) password
  ## Defaults to a random 10-character alphanumeric string if not set
  ##
  password: ""
  ## @param auth.existingSecret The name of an existing secret with Redis(R) credentials
  ## NOTE: When it's set, the previous `auth.password` parameter is ignored
  ##
  existingSecret: ""
  ## @param auth.existingSecretPasswordKey Password key to be retrieved from existing secret
  ## NOTE: ignored unless `auth.existingSecret` parameter is set
  ##
  existingSecretPasswordKey: ""
  ## @param auth.usePasswordFiles Mount credentials as files instead of using an environment variable
  ##
  usePasswordFiles: true
  ## @param auth.usePasswordFileFromSecret Mount password file from secret
  ##
  usePasswordFileFromSecret: true
  ## Redis ACL restricts connections by limiting commands and key access with auth management.
  ## ref: https://redis.io/docs/latest/operate/oss_and_stack/management/security/acl/
  ##
  acl:
    ## @param auth.acl.enabled Enables the support of the Redis ACL system
    ##
    enabled: false
    ## @param auth.acl.sentinel Enables the support of the Redis ACL system for Sentinel Nodes
    ##
    sentinel: false
    ## @param auth.acl.users A list of the configured users in the Redis ACL system
    ##
    ## Example:
    ## users:
    ##   - username: "my-user"
    ##     password: "mypassword"
    ##     enabled: "on"
    ##     commands: "+@all"
    ##     keys: "~*"
    ##     channels: "&*"
    users: []
    ## @param auth.acl.userSecret Name of the Secret, containing user credentials for ACL users. Keys must match usernames.
    userSecret: ""
## @param commonConfiguration [string] Common configuration to be added into the ConfigMap
## ref: https://redis.io/topics/config
##
commonConfiguration: |-
  # Enable AOF https://redis.io/topics/persistence#append-only-file
  appendonly yes
  # Disable RDB persistence, AOF persistence already enabled.
  save ""
## @param existingConfigmap The name of an existing ConfigMap with your custom configuration for Redis(R) nodes
##
existingConfigmap: ""
## @section Redis(R) master configuration parameters
##
master:
  ## @param master.count Number of Redis(R) master instances to deploy (experimental, requires additional configuration)
  ##
  count: 1
  ## @param master.revisionHistoryLimit The number of old history to retain to allow rollback
  ## NOTE: Explicitly setting this field to 0, will result in cleaning up all the history, breaking ability to rollback
  revisionHistoryLimit: 10
  ## @param master.configuration Configuration for Redis(R) master nodes
  ## ref: https://redis.io/topics/config
  ##
  configuration: ""
  ## @param master.disableCommands Array with Redis(R) commands to disable on master nodes
  ## Commands will be completely disabled by renaming each to an empty string.
  ## ref: https://redis.io/topics/security#disabling-of-specific-commands
  ##
  disableCommands:
    - FLUSHDB
    - FLUSHALL
  ## @param master.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param master.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param master.enableServiceLinks Whether information about services should be injected into pod's environment variable
  ##
  enableServiceLinks: true
  ## @param master.preExecCmds Additional commands to run prior to starting Redis(R) master
  ##
  preExecCmds: []
  ## @param master.extraFlags Array with additional command line flags for Redis(R) master
  ## e.g:
  ## extraFlags:
  ##  - "--maxmemory-policy volatile-ttl"
  ##  - "--repl-backlog-size 1024mb"
  ##
  extraFlags: []
  ## @param master.extraEnvVars Array with extra environment variables to add to Redis(R) master nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param master.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for Redis(R) master nodes
  ##
  extraEnvVarsCM: ""
  ## @param master.extraEnvVarsSecret Name of existing Secret containing extra env vars for Redis(R) master nodes
  ##
  extraEnvVarsSecret: ""
  ## @param master.containerPorts.redis Container port to open on Redis(R) master nodes
  ##
  containerPorts:
    redis: 6379
  ## Configure extra options for Redis(R) containers' liveness and readiness probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
  ## @param master.startupProbe.enabled Enable startupProbe on Redis(R) master nodes
  ## @param master.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param master.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param master.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param master.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param master.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 20
    periodSeconds: 5
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param master.livenessProbe.enabled Enable livenessProbe on Redis(R) master nodes
  ## @param master.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param master.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param master.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param master.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param master.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    initialDelaySeconds: 20
    periodSeconds: 5
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param master.readinessProbe.enabled Enable readinessProbe on Redis(R) master nodes
  ## @param master.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param master.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param master.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param master.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param master.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    initialDelaySeconds: 20
    periodSeconds: 5
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 5
  ## @param master.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param master.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param master.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## Redis(R) master resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param master.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if master.resources is set (master.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param master.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param master.podSecurityContext.enabled Enabled Redis(R) master pods' Security Context
  ## @param master.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param master.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param master.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param master.podSecurityContext.fsGroup Set Redis(R) master pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param master.containerSecurityContext.enabled Enabled Redis(R) master containers' Security Context
  ## @param master.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param master.containerSecurityContext.runAsUser Set Redis(R) master containers' Security Context runAsUser
  ## @param master.containerSecurityContext.runAsGroup Set Redis(R) master containers' Security Context runAsGroup
  ## @param master.containerSecurityContext.runAsNonRoot Set Redis(R) master containers' Security Context runAsNonRoot
  ## @param master.containerSecurityContext.allowPrivilegeEscalation Is it possible to escalate Redis(R) pod(s) privileges
  ## @param master.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context read-only root filesystem
  ## @param master.containerSecurityContext.seccompProfile.type Set Redis(R) master containers' Security Context seccompProfile
  ## @param master.containerSecurityContext.capabilities.drop Set Redis(R) master containers' Security Context capabilities to drop
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
  ## @param master.kind Use either Deployment, StatefulSet (default) or DaemonSet
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/
  ##
  kind: StatefulSet
  ## @param master.schedulerName Alternate scheduler for Redis(R) master pods
  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param master.updateStrategy.type Redis(R) master statefulset strategy type
  ## @skip master.updateStrategy.rollingUpdate
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    ## StrategyType
    ## Can be set to RollingUpdate, OnDelete (statefulset), Recreate (deployment)
    ##
    type: RollingUpdate
  ## @param master.minReadySeconds How many seconds a pod needs to be ready before killing the next, during update
  ##
  minReadySeconds: 0
  ## @param master.priorityClassName Redis(R) master pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param master.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param master.hostAliases Redis(R) master pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param master.podLabels Extra labels for Redis(R) master pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param master.podAnnotations Annotations for Redis(R) master pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param master.shareProcessNamespace Share a single process namespace between all of the containers in Redis(R) master pods
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/share-process-namespace/
  ##
  shareProcessNamespace: false
  ## @param master.podAffinityPreset Pod affinity preset. Ignored if `master.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param master.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `master.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node master.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param master.nodeAffinityPreset.type Node affinity preset type. Ignored if `master.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param master.nodeAffinityPreset.key Node label key to match. Ignored if `master.affinity` is set
    ##
    key: ""
    ## @param master.nodeAffinityPreset.values Node label values to match. Ignored if `master.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param master.affinity Affinity for Redis(R) master pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `master.podAffinityPreset`, `master.podAntiAffinityPreset`, and `master.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param master.nodeSelector Node labels for Redis(R) master pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector: {}
  ## @param master.tolerations Tolerations for Redis(R) master pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param master.topologySpreadConstraints Spread Constraints for Redis(R) master pod assignment
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/
  ## E.g.
  ## topologySpreadConstraints:
  ##   - maxSkew: 1
  ##     topologyKey: node
  ##     whenUnsatisfiable: DoNotSchedule
  ##
  topologySpreadConstraints: []
  ## @param master.dnsPolicy DNS Policy for Redis(R) master pod
  ## ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/
  ## E.g.
  ## dnsPolicy: ClusterFirst
  ##
  dnsPolicy: ""
  ## @param master.dnsConfig DNS Configuration for Redis(R) master pod
  ## ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/
  ## E.g.
  ## dnsConfig:
  ##   options:
  ##   - name: ndots
  ##     value: "4"
  ##   - name: single-request-reopen
  ##
  dnsConfig: {}
  ## @param master.lifecycleHooks for the Redis(R) master container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param master.extraVolumes Optionally specify extra list of additional volumes for the Redis(R) master pod(s)
  ##
  extraVolumes: []
  ## @param master.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Redis(R) master container(s)
  ##
  extraVolumeMounts: []
  ## @param master.sidecars Add additional sidecar containers to the Redis(R) master pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param master.initContainers Add additional init containers to the Redis(R) master pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['/bin/bash', '-ec', 'echo "hello world"']
  ##
  initContainers: []
  ## Persistence parameters
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
  ##
  persistence:
    ## @param master.persistence.enabled Enable persistence on Redis(R) master nodes using Persistent Volume Claims
    ##
    enabled: true
    ## @param master.persistence.medium Provide a medium for `emptyDir` volumes.
    ##
    medium: ""
    ## @param master.persistence.sizeLimit Set this to enable a size limit for `emptyDir` volumes.
    ##
    sizeLimit: ""
    ## @param master.persistence.path The path the volume will be mounted at on Redis(R) master containers
    ## NOTE: Useful when using different Redis(R) images
    ##
    path: /data
    ## @param master.persistence.subPath The subdirectory of the volume to mount on Redis(R) master containers
    ## NOTE: Useful in dev environments
    ##
    subPath: ""
    ## @param master.persistence.subPathExpr Used to construct the subPath subdirectory of the volume to mount on Redis(R) master containers
    ##
    subPathExpr: ""
    ## @param master.persistence.storageClass Persistent Volume storage class
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is set, choosing the default provisioner
    ##
    storageClass: ""
    ## @param master.persistence.accessModes Persistent Volume access modes
    ##
    accessModes:
      - ReadWriteOnce
    ## @param master.persistence.size Persistent Volume size
    ##
    size: 8Gi
    ## @param master.persistence.annotations Additional custom annotations for the PVC
    ##
    annotations: {}
    ## @param master.persistence.labels Additional custom labels for the PVC
    ##
    labels: {}
    ## @param master.persistence.selector Additional labels to match for the PVC
    ## e.g:
    ## selector:
    ##   matchLabels:
    ##     app: my-app
    ##
    selector: {}
    ## @param master.persistence.dataSource Custom PVC data source
    ##
    dataSource: {}
    ## @param master.persistence.existingClaim Use a existing PVC which must be created manually before bound
    ## NOTE: requires master.persistence.enabled: true
    ##
    existingClaim: ""
  ## persistentVolumeClaimRetentionPolicy
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#persistentvolumeclaim-retention
  ## @param master.persistentVolumeClaimRetentionPolicy.enabled Controls if and how PVCs are deleted during the lifecycle of a StatefulSet
  ## @param master.persistentVolumeClaimRetentionPolicy.whenScaled Volume retention behavior when the replica count of the StatefulSet is reduced
  ## @param master.persistentVolumeClaimRetentionPolicy.whenDeleted Volume retention behavior that applies when the StatefulSet is deleted
  ##
  persistentVolumeClaimRetentionPolicy:
    enabled: false
    whenScaled: Retain
    whenDeleted: Retain
  ## Redis(R) master service parameters
  ##
  service:
    ## @param master.service.type Redis(R) master service type
    ##
    type: ClusterIP
    ## @param master.service.portNames.redis Redis(R) master service port name
    ##
    portNames:
      redis: "tcp-redis"
    ## @param master.service.ports.redis Redis(R) master service port
    ##
    ports:
      redis: 6379
    ## @param master.service.nodePorts.redis Node port for Redis(R) master
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
    ## NOTE: choose port between <30000-32767>
    ##
    nodePorts:
      redis: ""
    ## @param master.service.externalTrafficPolicy Redis(R) master service external traffic policy
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param master.service.extraPorts Extra ports to expose (normally used with the `sidecar` value)
    ##
    extraPorts: []
    ## @param master.service.internalTrafficPolicy Redis(R) master service internal traffic policy (requires Kubernetes v1.22 or greater to be usable)
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service-traffic-policy/
    ##
    internalTrafficPolicy: Cluster
    ## @param master.service.clusterIP Redis(R) master service Cluster IP
    ##
    clusterIP: ""
    ## @param master.service.loadBalancerIP Redis(R) master service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
    ##
    loadBalancerIP: ""
    ## @param master.service.loadBalancerClass master service Load Balancer class if service type is `LoadBalancer` (optional, cloud specific)
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerClass: ""
    ## @param master.service.loadBalancerSourceRanges Redis(R) master service Load Balancer sources
    ## https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g.
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param master.service.externalIPs Redis(R) master service External IPs
    ## https://kubernetes.io/docs/concepts/services-networking/service/#external-ips
    ## e.g.
    ## externalIPs:
    ##   - **********
    ##   - ***********
    ##
    externalIPs: []
    ## @param master.service.annotations Additional custom annotations for Redis(R) master service
    ##
    annotations: {}
    ## @param master.service.sessionAffinity Session Affinity for Kubernetes service, can be "None" or "ClientIP"
    ## If "ClientIP", consecutive client requests will be directed to the same Pod
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies
    ##
    sessionAffinity: None
    ## @param master.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
  ## @param master.terminationGracePeriodSeconds Integer setting the termination grace period for the redis-master pods
  ##
  terminationGracePeriodSeconds: 30
  ## ServiceAccount configuration
  ##
  serviceAccount:
    ## @param master.serviceAccount.create Specifies whether a ServiceAccount should be created
    ##
    create: true
    ## @param master.serviceAccount.name The name of the ServiceAccount to use.
    ## If not set and create is true, a name is generated using the common.names.fullname template
    ##
    name: ""
    ## @param master.serviceAccount.automountServiceAccountToken Whether to auto mount the service account token
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/#use-the-default-service-account-to-access-the-api-server
    ##
    automountServiceAccountToken: false
    ## @param master.serviceAccount.annotations Additional custom annotations for the ServiceAccount
    ##
    annotations: {}
  ## Pod Disruption Budget configuration
  ## ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb
  ## @param master.pdb.create Enable/disable a Pod Disruption Budget creation
  ## @param master.pdb.minAvailable [object] Minimum number/percentage of pods that should remain scheduled
  ## @param master.pdb.maxUnavailable [object] Maximum number/percentage of pods that may be made unavailable. Defaults to `1` if both `master.pdb.minAvailable` and `master.pdb.maxUnavailable` are empty.
  ##
  pdb:
    create: true
    minAvailable: ""
    maxUnavailable: ""
  ## @param master.extraPodSpec Optionally specify extra PodSpec for the Redis(R) master pod(s)
  ##
  extraPodSpec: {}
  ## @param master.annotations Additional custom annotations for Redis(R) Master resource
  ##
  annotations: {}
## @section Redis(R) replicas configuration parameters
##
replica:
  ## @param replica.kind Use either DaemonSet or StatefulSet (default)
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/
  ##
  kind: StatefulSet
  ## @param replica.replicaCount Number of Redis(R) replicas to deploy
  ##
  replicaCount: 3
  ## @param replica.revisionHistoryLimit The number of old history to retain to allow rollback
  ## NOTE: Explicitly setting this field to 0, will result in cleaning up all the history, breaking ability to rollback
  revisionHistoryLimit: 10
  ## @param replica.configuration Configuration for Redis(R) replicas nodes
  ## ref: https://redis.io/topics/config
  ##
  configuration: ""
  ## @param replica.disableCommands Array with Redis(R) commands to disable on replicas nodes
  ## Commands will be completely disabled by renaming each to an empty string.
  ## ref: https://redis.io/topics/security#disabling-of-specific-commands
  ##
  disableCommands:
    - FLUSHDB
    - FLUSHALL
  ## @param replica.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param replica.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param replica.enableServiceLinks Whether information about services should be injected into pod's environment variable
  ##
  enableServiceLinks: true
  ## @param replica.preExecCmds Additional commands to run prior to starting Redis(R) replicas
  ##
  preExecCmds: []
  ## @param replica.extraFlags Array with additional command line flags for Redis(R) replicas
  ## e.g:
  ## extraFlags:
  ##  - "--maxmemory-policy volatile-ttl"
  ##  - "--repl-backlog-size 1024mb"
  ##
  extraFlags: []
  ## @param replica.extraEnvVars Array with extra environment variables to add to Redis(R) replicas nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param replica.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for Redis(R) replicas nodes
  ##
  extraEnvVarsCM: ""
  ## @param replica.extraEnvVarsSecret Name of existing Secret containing extra env vars for Redis(R) replicas nodes
  ##
  extraEnvVarsSecret: ""
  ## @param replica.externalMaster.enabled Use external master for bootstrapping
  ## @param replica.externalMaster.host External master host to bootstrap from
  ## @param replica.externalMaster.port Port for Redis service external master host
  ##
  externalMaster:
    enabled: false
    host: ""
    port: 6379
  ## @param replica.containerPorts.redis Container port to open on Redis(R) replicas nodes
  ##
  containerPorts:
    redis: 6379
  ## Configure extra options for Redis(R) containers' liveness and readiness probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
  ## @param replica.startupProbe.enabled Enable startupProbe on Redis(R) replicas nodes
  ## @param replica.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param replica.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param replica.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param replica.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param replica.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: true
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 22
  ## @param replica.livenessProbe.enabled Enable livenessProbe on Redis(R) replicas nodes
  ## @param replica.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param replica.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param replica.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param replica.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param replica.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    initialDelaySeconds: 20
    periodSeconds: 5
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param replica.readinessProbe.enabled Enable readinessProbe on Redis(R) replicas nodes
  ## @param replica.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param replica.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param replica.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param replica.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param replica.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    initialDelaySeconds: 20
    periodSeconds: 5
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 5
  ## @param replica.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param replica.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param replica.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## Redis(R) replicas resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param replica.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if replica.resources is set (replica.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param replica.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param replica.podSecurityContext.enabled Enabled Redis(R) replicas pods' Security Context
  ## @param replica.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param replica.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param replica.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param replica.podSecurityContext.fsGroup Set Redis(R) replicas pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param replica.containerSecurityContext.enabled Enabled Redis(R) replicas containers' Security Context
  ## @param replica.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param replica.containerSecurityContext.runAsUser Set Redis(R) replicas containers' Security Context runAsUser
  ## @param replica.containerSecurityContext.runAsGroup Set Redis(R) replicas containers' Security Context runAsGroup
  ## @param replica.containerSecurityContext.runAsNonRoot Set Redis(R) replicas containers' Security Context runAsNonRoot
  ## @param replica.containerSecurityContext.allowPrivilegeEscalation Set Redis(R) replicas pod's Security Context allowPrivilegeEscalation
  ## @param replica.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context read-only root filesystem
  ## @param replica.containerSecurityContext.seccompProfile.type Set Redis(R) replicas containers' Security Context seccompProfile
  ## @param replica.containerSecurityContext.capabilities.drop Set Redis(R) replicas containers' Security Context capabilities to drop
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
  ## @param replica.schedulerName Alternate scheduler for Redis(R) replicas pods
  ## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param replica.updateStrategy.type Redis(R) replicas statefulset strategy type
  ## @skip replica.updateStrategy.rollingUpdate
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    ## StrategyType
    ## Can be set to RollingUpdate, OnDelete (statefulset), Recreate (deployment)
    ##
    type: RollingUpdate
  ## @param replica.minReadySeconds How many seconds a pod needs to be ready before killing the next, during update
  ##
  minReadySeconds: 0
  ## @param replica.priorityClassName Redis(R) replicas pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param replica.podManagementPolicy podManagementPolicy to manage scaling operation of %%MAIN_CONTAINER_NAME%% pods
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#pod-management-policies
  ##
  podManagementPolicy: ""
  ## @param replica.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param replica.hostAliases Redis(R) replicas pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param replica.podLabels Extra labels for Redis(R) replicas pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param replica.podAnnotations Annotations for Redis(R) replicas pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param replica.shareProcessNamespace Share a single process namespace between all of the containers in Redis(R) replicas pods
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/share-process-namespace/
  ##
  shareProcessNamespace: false
  ## @param replica.podAffinityPreset Pod affinity preset. Ignored if `replica.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param replica.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `replica.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param replica.nodeAffinityPreset.type Node affinity preset type. Ignored if `replica.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param replica.nodeAffinityPreset.key Node label key to match. Ignored if `replica.affinity` is set
    ##
    key: ""
    ## @param replica.nodeAffinityPreset.values Node label values to match. Ignored if `replica.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param replica.affinity Affinity for Redis(R) replicas pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `replica.podAffinityPreset`, `replica.podAntiAffinityPreset`, and `replica.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param replica.nodeSelector Node labels for Redis(R) replicas pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector: {}
  ## @param replica.tolerations Tolerations for Redis(R) replicas pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param replica.topologySpreadConstraints Spread Constraints for Redis(R) replicas pod assignment
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/
  ## E.g.
  ## topologySpreadConstraints:
  ##   - maxSkew: 1
  ##     topologyKey: node
  ##     whenUnsatisfiable: DoNotSchedule
  ##
  topologySpreadConstraints: []
  ## @param replica.dnsPolicy DNS Policy for Redis(R) replica pods
  ## ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/
  ## E.g.
  ## dnsPolicy: ClusterFirst
  ##
  dnsPolicy: ""
  ## @param replica.dnsConfig DNS Configuration for Redis(R) replica pods
  ## ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/
  ## E.g.
  ## dnsConfig:
  ##   options:
  ##   - name: ndots
  ##     value: "4"
  ##   - name: single-request-reopen
  ##
  dnsConfig: {}
  ## @param replica.lifecycleHooks for the Redis(R) replica container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param replica.extraVolumes Optionally specify extra list of additional volumes for the Redis(R) replicas pod(s)
  ##
  extraVolumes: []
  ## @param replica.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Redis(R) replicas container(s)
  ##
  extraVolumeMounts: []
  ## @param replica.sidecars Add additional sidecar containers to the Redis(R) replicas pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param replica.initContainers Add additional init containers to the Redis(R) replicas pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['/bin/bash', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## Persistence Parameters
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
  ##
  persistence:
    ## @param replica.persistence.enabled Enable persistence on Redis(R) replicas nodes using Persistent Volume Claims
    ##
    enabled: true
    ## @param replica.persistence.medium Provide a medium for `emptyDir` volumes.
    ##
    medium: ""
    ## @param replica.persistence.sizeLimit Set this to enable a size limit for `emptyDir` volumes.
    ##
    sizeLimit: ""
    ##  @param replica.persistence.path The path the volume will be mounted at on Redis(R) replicas containers
    ## NOTE: Useful when using different Redis(R) images
    ##
    path: /data
    ## @param replica.persistence.subPath The subdirectory of the volume to mount on Redis(R) replicas containers
    ## NOTE: Useful in dev environments
    ##
    subPath: ""
    ## @param replica.persistence.subPathExpr Used to construct the subPath subdirectory of the volume to mount on Redis(R) replicas containers
    ##
    subPathExpr: ""
    ## @param replica.persistence.storageClass Persistent Volume storage class
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is set, choosing the default provisioner
    ##
    storageClass: ""
    ## @param replica.persistence.accessModes Persistent Volume access modes
    ##
    accessModes:
      - ReadWriteOnce
    ## @param replica.persistence.size Persistent Volume size
    ##
    size: 8Gi
    ## @param replica.persistence.annotations Additional custom annotations for the PVC
    ##
    annotations: {}
    ## @param replica.persistence.labels Additional custom labels for the PVC
    ##
    labels: {}
    ## @param replica.persistence.selector Additional labels to match for the PVC
    ## e.g:
    ## selector:
    ##   matchLabels:
    ##     app: my-app
    ##
    selector: {}
    ## @param replica.persistence.dataSource Custom PVC data source
    ##
    dataSource: {}
    ## @param replica.persistence.existingClaim Use a existing PVC which must be created manually before bound
    ## NOTE: requires replica.persistence.enabled: true
    ##
    existingClaim: ""
  ## persistentVolumeClaimRetentionPolicy
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#persistentvolumeclaim-retention
  ## @param replica.persistentVolumeClaimRetentionPolicy.enabled Controls if and how PVCs are deleted during the lifecycle of a StatefulSet
  ## @param replica.persistentVolumeClaimRetentionPolicy.whenScaled Volume retention behavior when the replica count of the StatefulSet is reduced
  ## @param replica.persistentVolumeClaimRetentionPolicy.whenDeleted Volume retention behavior that applies when the StatefulSet is deleted
  ##
  persistentVolumeClaimRetentionPolicy:
    enabled: false
    whenScaled: Retain
    whenDeleted: Retain
  ## Redis(R) replicas service parameters
  ##
  service:
    ## @param replica.service.type Redis(R) replicas service type
    ##
    type: ClusterIP
    ## @param replica.service.ports.redis Redis(R) replicas service port
    ##
    ports:
      redis: 6379
    ## @param replica.service.nodePorts.redis Node port for Redis(R) replicas
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
    ## NOTE: choose port between <30000-32767>
    ##
    nodePorts:
      redis: ""
    ## @param replica.service.externalTrafficPolicy Redis(R) replicas service external traffic policy
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param replica.service.internalTrafficPolicy Redis(R) replicas service internal traffic policy (requires Kubernetes v1.22 or greater to be usable)
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service-traffic-policy/
    ##
    internalTrafficPolicy: Cluster
    ## @param replica.service.extraPorts Extra ports to expose (normally used with the `sidecar` value)
    ##
    extraPorts: []
    ## @param replica.service.clusterIP Redis(R) replicas service Cluster IP
    ##
    clusterIP: ""
    ## @param replica.service.loadBalancerIP Redis(R) replicas service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
    ##
    loadBalancerIP: ""
    ## @param replica.service.loadBalancerClass replicas service Load Balancer class if service type is `LoadBalancer` (optional, cloud specific)
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerClass: ""
    ## @param replica.service.loadBalancerSourceRanges Redis(R) replicas service Load Balancer sources
    ## https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g.
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param replica.service.annotations Additional custom annotations for Redis(R) replicas service
    ##
    annotations: {}
    ## @param replica.service.sessionAffinity Session Affinity for Kubernetes service, can be "None" or "ClientIP"
    ## If "ClientIP", consecutive client requests will be directed to the same Pod
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies
    ##
    sessionAffinity: None
    ## @param replica.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
  ## @param replica.terminationGracePeriodSeconds Integer setting the termination grace period for the redis-replicas pods
  ##
  terminationGracePeriodSeconds: 30
  ## Autoscaling configuration
  ##
  autoscaling:
    ## @param replica.autoscaling.enabled Enable replica autoscaling settings
    ##
    enabled: false
    ## @param replica.autoscaling.minReplicas Minimum replicas for the pod autoscaling
    ##
    minReplicas: 1
    ## @param replica.autoscaling.maxReplicas Maximum replicas for the pod autoscaling
    ##
    maxReplicas: 11
    ## @param replica.autoscaling.targetCPU Percentage of CPU to consider when autoscaling
    ##
    targetCPU: ""
    ## @param replica.autoscaling.targetMemory Percentage of Memory to consider when autoscaling
    ##
    targetMemory: ""
  ## ServiceAccount configuration
  ##
  serviceAccount:
    ## @param replica.serviceAccount.create Specifies whether a ServiceAccount should be created
    ##
    create: true
    ## @param replica.serviceAccount.name The name of the ServiceAccount to use.
    ## If not set and create is true, a name is generated using the common.names.fullname template
    ##
    name: ""
    ## @param replica.serviceAccount.automountServiceAccountToken Whether to auto mount the service account token
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/#use-the-default-service-account-to-access-the-api-server
    ##
    automountServiceAccountToken: false
    ## @param replica.serviceAccount.annotations Additional custom annotations for the ServiceAccount
    ##
    annotations: {}
  ## Pod Disruption Budget configuration
  ## ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb
  ## @param replica.pdb.create Enable/disable a Pod Disruption Budget creation
  ## @param replica.pdb.minAvailable [object] Minimum number/percentage of pods that should remain scheduled
  ## @param replica.pdb.maxUnavailable [object] Maximum number/percentage of pods that may be made unavailable. Defaults to `1` if both `replica.pdb.minAvailable` and `replica.pdb.maxUnavailable` are empty.
  ##
  pdb:
    create: true
    minAvailable: ""
    maxUnavailable: ""
  ## @param replica.extraPodSpec Optionally specify extra PodSpec for the Redis(R) replicas pod(s)
  ##
  extraPodSpec: {}
  ## @param replica.annotations Additional custom annotations for Redis(R) replicas resource
  ##
  annotations: {}
## @section Redis(R) Sentinel configuration parameters
##

sentinel:
  ## @param sentinel.enabled Use Redis(R) Sentinel on Redis(R) pods.
  ## IMPORTANT: this will disable the master and replicas services and
  ## create a single Redis(R) service exposing both the Redis and Sentinel ports
  ##
  enabled: false
  ## Bitnami Redis(R) Sentinel image version
  ## ref: https://hub.docker.com/r/bitnami/redis-sentinel/tags/
  ## @param sentinel.image.registry [default: REGISTRY_NAME] Redis(R) Sentinel image registry
  ## @param sentinel.image.repository [default: REPOSITORY_NAME/redis-sentinel] Redis(R) Sentinel image repository
  ## @skip sentinel.image.tag Redis(R) Sentinel image tag (immutable tags are recommended)
  ## @param sentinel.image.digest Redis(R) Sentinel image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param sentinel.image.pullPolicy Redis(R) Sentinel image pull policy
  ## @param sentinel.image.pullSecrets Redis(R) Sentinel image pull secrets
  ## @param sentinel.image.debug Enable image debug mode
  ##
  image:
    registry: docker.io
    repository: bitnami/redis-sentinel
    tag: 8.0.2-debian-12-r2
    digest: ""
    ## Specify a imagePullPolicy
    ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
    ## Enable debug mode
    ##
    debug: false
  ## @param sentinel.annotations Additional custom annotations for Redis(R) Sentinel resource
  ##
  annotations: {}
  ## @param sentinel.masterSet Master set name
  ##
  masterSet: mymaster
  ## @param sentinel.quorum Sentinel Quorum
  ##
  quorum: 2
  ## @param sentinel.getMasterTimeout Amount of time to allow before get_sentinel_master_info() times out.
  ##
  getMasterTimeout: 90
  ## @param sentinel.automateClusterRecovery Automate cluster recovery in cases where the last replica is not considered a good replica and Sentinel won't automatically failover to it.
  ## This also prevents any new replica from starting until the last remaining replica is elected as master to guarantee that it is the one to be elected by Sentinel, and not a newly started replica with no data.
  ## NOTE: This feature requires a "downAfterMilliseconds" value less or equal to 2000.
  ##
  automateClusterRecovery: false
  ## @param sentinel.redisShutdownWaitFailover Whether the Redis(R) master container waits for the failover at shutdown (in addition to the Redis(R) Sentinel container).
  ##
  redisShutdownWaitFailover: true
  ## Sentinel timing restrictions
  ## @param sentinel.downAfterMilliseconds Timeout for detecting a Redis(R) node is down
  ## @param sentinel.failoverTimeout Timeout for performing a election failover
  ##
  downAfterMilliseconds: 60000
  failoverTimeout: 180000
  ## @param sentinel.parallelSyncs Number of replicas that can be reconfigured in parallel to use the new master after a failover
  ##
  parallelSyncs: 1
  ## @param sentinel.configuration Configuration for Redis(R) Sentinel nodes
  ## ref: https://redis.io/topics/sentinel
  ##
  configuration: ""
  ## @param sentinel.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param sentinel.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param sentinel.enableServiceLinks Whether information about services should be injected into pod's environment variable
  ##
  enableServiceLinks: true
  ## @param sentinel.preExecCmds Additional commands to run prior to starting Redis(R) Sentinel
  ##
  preExecCmds: []
  ## @param sentinel.extraEnvVars Array with extra environment variables to add to Redis(R) Sentinel nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param sentinel.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for Redis(R) Sentinel nodes
  ##
  extraEnvVarsCM: ""
  ## @param sentinel.extraEnvVarsSecret Name of existing Secret containing extra env vars for Redis(R) Sentinel nodes
  ##
  extraEnvVarsSecret: ""
  ## @param sentinel.externalMaster.enabled Use external master for bootstrapping
  ## @param sentinel.externalMaster.host External master host to bootstrap from
  ## @param sentinel.externalMaster.port Port for Redis service external master host
  ##
  externalMaster:
    enabled: false
    host: ""
    port: 6379
  ## @param sentinel.containerPorts.sentinel Container port to open on Redis(R) Sentinel nodes
  ##
  containerPorts:
    sentinel: 26379
  ## Configure extra options for Redis(R) containers' liveness and readiness probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
  ## @param sentinel.startupProbe.enabled Enable startupProbe on Redis(R) Sentinel nodes
  ## @param sentinel.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param sentinel.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param sentinel.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param sentinel.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param sentinel.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: true
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 22
  ## @param sentinel.livenessProbe.enabled Enable livenessProbe on Redis(R) Sentinel nodes
  ## @param sentinel.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param sentinel.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param sentinel.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param sentinel.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param sentinel.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    initialDelaySeconds: 20
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 6
  ## @param sentinel.readinessProbe.enabled Enable readinessProbe on Redis(R) Sentinel nodes
  ## @param sentinel.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param sentinel.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param sentinel.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param sentinel.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param sentinel.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    initialDelaySeconds: 20
    periodSeconds: 5
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 6
  ## @param sentinel.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param sentinel.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param sentinel.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## Persistence parameters
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
  ##
  persistence:
    ## @param sentinel.persistence.enabled Enable persistence on Redis(R) sentinel nodes using Persistent Volume Claims (Experimental)
    ##
    enabled: false
    ## @param sentinel.persistence.storageClass Persistent Volume storage class
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is set, choosing the default provisioner
    ##
    storageClass: ""
    ## @param sentinel.persistence.accessModes Persistent Volume access modes
    ##
    accessModes:
      - ReadWriteOnce
    ## @param sentinel.persistence.size Persistent Volume size
    ##
    size: 100Mi
    ## @param sentinel.persistence.annotations Additional custom annotations for the PVC
    ##
    annotations: {}
    ## @param sentinel.persistence.labels Additional custom labels for the PVC
    ##
    labels: {}
    ## @param sentinel.persistence.selector Additional labels to match for the PVC
    ## e.g:
    ## selector:
    ##   matchLabels:
    ##     app: my-app
    ##
    selector: {}
    ## @param sentinel.persistence.dataSource Custom PVC data source
    ##
    dataSource: {}
    ## @param sentinel.persistence.medium Provide a medium for `emptyDir` volumes.
    ##
    medium: ""
    ## @param sentinel.persistence.sizeLimit Set this to enable a size limit for `emptyDir` volumes.
    ##
    sizeLimit: ""
  ## persistentVolumeClaimRetentionPolicy
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#persistentvolumeclaim-retention
  ## @param sentinel.persistentVolumeClaimRetentionPolicy.enabled Controls if and how PVCs are deleted during the lifecycle of a StatefulSet
  ## @param sentinel.persistentVolumeClaimRetentionPolicy.whenScaled Volume retention behavior when the replica count of the StatefulSet is reduced
  ## @param sentinel.persistentVolumeClaimRetentionPolicy.whenDeleted Volume retention behavior that applies when the StatefulSet is deleted
  ##
  persistentVolumeClaimRetentionPolicy:
    enabled: false
    whenScaled: Retain
    whenDeleted: Retain
  ## Redis(R) Sentinel resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param sentinel.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if sentinel.resources is set (sentinel.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param sentinel.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param sentinel.containerSecurityContext.enabled Enabled Redis(R) Sentinel containers' Security Context
  ## @param sentinel.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param sentinel.containerSecurityContext.runAsUser Set Redis(R) Sentinel containers' Security Context runAsUser
  ## @param sentinel.containerSecurityContext.runAsGroup Set Redis(R) Sentinel containers' Security Context runAsGroup
  ## @param sentinel.containerSecurityContext.runAsNonRoot Set Redis(R) Sentinel containers' Security Context runAsNonRoot
  ## @param sentinel.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context read-only root filesystem
  ## @param sentinel.containerSecurityContext.allowPrivilegeEscalation Set Redis(R) Sentinel containers' Security Context allowPrivilegeEscalation
  ## @param sentinel.containerSecurityContext.seccompProfile.type Set Redis(R) Sentinel containers' Security Context seccompProfile
  ## @param sentinel.containerSecurityContext.capabilities.drop Set Redis(R) Sentinel containers' Security Context capabilities to drop
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
  ## @param sentinel.lifecycleHooks for the Redis(R) sentinel container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param sentinel.extraVolumes Optionally specify extra list of additional volumes for the Redis(R) Sentinel
  ##
  extraVolumes: []
  ## @param sentinel.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Redis(R) Sentinel container(s)
  ##
  extraVolumeMounts: []
  ## Redis(R) Sentinel service parameters
  ## Note: values passed in this section also configure the master service, unless the sentinel.masterService is explicitly overridden.
  service:
    ## @param sentinel.service.type Redis(R) Sentinel service type
    ##
    type: ClusterIP
    ## @param sentinel.service.ports.redis Redis(R) service port for Redis(R)
    ## @param sentinel.service.ports.sentinel Redis(R) service port for Redis(R) Sentinel
    ##
    ports:
      redis: 6379
      sentinel: 26379
    ## @param sentinel.service.nodePorts.redis Node port for Redis(R)
    ## @param sentinel.service.nodePorts.sentinel Node port for Sentinel
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
    ## NOTE: choose port between <30000-32767>
    ## NOTE: By leaving these values blank, they will be generated by ports-configmap
    ##       If setting manually, please leave at least replica.replicaCount + 1 in between sentinel.service.nodePorts.redis and sentinel.service.nodePorts.sentinel to take into account the ports that will be created while incrementing that base port
    ##
    nodePorts:
      redis: ""
      sentinel: ""
    ## @param sentinel.service.externalTrafficPolicy Redis(R) Sentinel service external traffic policy
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param sentinel.service.extraPorts Extra ports to expose (normally used with the `sidecar` value)
    ##
    extraPorts: []
    ## @param sentinel.service.clusterIP Redis(R) Sentinel service Cluster IP
    ##
    clusterIP: ""
    ## @param sentinel.service.createMaster Enable master service pointing to the current master (experimental)
    ## NOTE: rbac.create need to be set to true
    ##
    createMaster: false

    ## @param sentinel.service.loadBalancerIP Redis(R) Sentinel service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
    ##
    loadBalancerIP: ""
    ## @param sentinel.service.loadBalancerClass sentinel service Load Balancer class if service type is `LoadBalancer` (optional, cloud specific)
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerClass: ""
    ## @param sentinel.service.loadBalancerSourceRanges Redis(R) Sentinel service Load Balancer sources
    ## https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g.
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param sentinel.service.annotations Additional custom annotations for Redis(R) Sentinel service
    ##
    annotations: {}
    ## @param sentinel.service.sessionAffinity Session Affinity for Kubernetes service, can be "None" or "ClientIP"
    ## If "ClientIP", consecutive client requests will be directed to the same Pod
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies
    ##
    sessionAffinity: None
    ## @param sentinel.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## Headless service properties
    ##
    headless:
      ## @param sentinel.service.headless.annotations Annotations for the headless service.
      ##
      annotations: {}
      ## @param sentinel.service.headless.extraPorts Optionally specify extra ports to expose for the headless service.
      ## Example:
      ## extraPorts:
      ##   - name: my-custom-port
      ##     port: 12345
      ##     protocol: TCP
      ##     targetPort: 12345
      ##
      extraPorts: []
  ## Redis(R) master service parameters
  ##
  masterService:
    ## @param sentinel.masterService.enabled Enable master service pointing to the current master (experimental)
    ## NOTE: rbac.create need to be set to true
    ##
    enabled: false
    ## @param sentinel.masterService.type Redis(R) Sentinel master service type
    ##
    type: ClusterIP
    ## @param sentinel.masterService.ports.redis Redis(R) service port for Redis(R)
    ##
    ports:
      redis: 6379
    ## @param sentinel.masterService.nodePorts.redis Node port for Redis(R)
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
    ## NOTE: choose port between <30000-32767>
    ## NOTE: By leaving these values blank, they will be generated by ports-configmap
    ##       If setting manually, please leave at least replica.replicaCount + 1 in between sentinel.service.nodePorts.redis and sentinel.service.nodePorts.sentinel to take into account the ports that will be created while incrementing that base port
    ##
    nodePorts:
      redis: ""
    ## @param sentinel.masterService.externalTrafficPolicy Redis(R) master service external traffic policy
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: ""
    ## @param sentinel.masterService.extraPorts Extra ports to expose (normally used with the `sidecar` value)
    ##
    extraPorts: []
    ## @param sentinel.masterService.clusterIP Redis(R) master service Cluster IP
    ##
    clusterIP: ""
    ## @param sentinel.masterService.loadBalancerIP Redis(R) master service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
    ##
    loadBalancerIP: ""
    ## @param sentinel.masterService.loadBalancerClass master service Load Balancer class if service type is `LoadBalancer` (optional, cloud specific)
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerClass: ""
    ## @param sentinel.masterService.loadBalancerSourceRanges Redis(R) master service Load Balancer sources
    ## https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g.
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param sentinel.masterService.annotations Additional custom annotations for Redis(R) master service
    ##
    annotations: {}
    ## @param sentinel.masterService.sessionAffinity Session Affinity for Kubernetes service, can be "None" or "ClientIP"
    ## If "ClientIP", consecutive client requests will be directed to the same Pod
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#virtual-ips-and-service-proxies
    ##
    sessionAffinity: None
    ## @param sentinel.masterService.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
  ## @param sentinel.terminationGracePeriodSeconds Integer setting the termination grace period for the redis-node pods
  ##
  terminationGracePeriodSeconds: 30
  ## @param sentinel.extraPodSpec Optionally specify extra PodSpec for the Redis(R) Sentinel pod(s)
  ##
  extraPodSpec: {}

  externalAccess:
    ## @param sentinel.externalAccess.enabled Enable external access to the Redis
    ##
    enabled: false
    service:
        ## @param sentinel.externalAccess.service.loadBalancerIPAnnotaion Name of annotation to specify fixed IP for service in.
        ##
      loadBalancerIPAnnotaion: ""
        ## @param sentinel.externalAccess.service.type Type for the services used to expose every Pod
        ## At this moment only LoadBalancer is supported
        ##
      type: LoadBalancer
      ## @param sentinel.externalAccess.service.redisPort Port for the services used to expose redis-server
        ##
      redisPort: 6379

      ## @param sentinel.externalAccess.service.sentinelPort Port for the services used to expose redis-sentinel
        ##
      sentinelPort: 26379
        ## @param sentinel.externalAccess.service.loadBalancerIP Array of load balancer IPs for each Redis(R) node. Length must be the same as sentinel.replicaCount
        ##
      loadBalancerIP: []
        ## @param sentinel.externalAccess.service.loadBalancerClass Load Balancer class if service type is `LoadBalancer` (optional, cloud specific)
        ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
        ##
      loadBalancerClass: ""
        ## @param sentinel.externalAccess.service.loadBalancerSourceRanges Service Load Balancer sources
        ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
        ## e.g:
        ## loadBalancerSourceRanges:
        ##   - **********/24
        ##
      loadBalancerSourceRanges: []
        ## @param sentinel.externalAccess.service.annotations Annotations to add to the services used to expose every Pod of the Redis(R) Cluster
        ##
      annotations: {}

## @section Other Parameters
##

## @param serviceBindings.enabled Create secret for service binding (Experimental)
## Ref: https://servicebinding.io/service-provider/
##
serviceBindings:
  enabled: false
## Network Policy configuration
## ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
##
networkPolicy:
  ## @param networkPolicy.enabled Enable creation of NetworkPolicy resources
  ##
  enabled: true
  ## @param networkPolicy.allowExternal Don't require client label for connections
  ## When set to false, only pods with the correct client label will have network access to the ports
  ## Redis(R) is listening on. When true, Redis(R) will accept connections from any source
  ## (with the correct destination port).
  ##
  allowExternal: true
  ## @param networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
  ##
  allowExternalEgress: true
  ## @param networkPolicy.extraIngress Add extra ingress rules to the NetworkPolicy
  ## e.g:
  ## extraIngress:
  ##   - ports:
  ##       - port: 1234
  ##     from:
  ##       - podSelector:
  ##           - matchLabels:
  ##               - role: frontend
  ##       - podSelector:
  ##           - matchExpressions:
  ##               - key: role
  ##                 operator: In
  ##                 values:
  ##                   - frontend
  ##
  extraIngress: []
  ## @param networkPolicy.extraEgress Add extra egress rules to the NetworkPolicy
  ## e.g:
  ## extraEgress:
  ##   - ports:
  ##       - port: 1234
  ##     to:
  ##       - podSelector:
  ##           - matchLabels:
  ##               - role: frontend
  ##       - podSelector:
  ##           - matchExpressions:
  ##               - key: role
  ##                 operator: In
  ##                 values:
  ##                   - frontend
  ##
  extraEgress: []
  ## @param networkPolicy.ingressNSMatchLabels Labels to match to allow traffic from other namespaces
  ## @param networkPolicy.ingressNSPodMatchLabels Pod labels to match to allow traffic from other namespaces
  ##
  ingressNSMatchLabels: {}
  ingressNSPodMatchLabels: {}
  metrics:
    ## @param networkPolicy.metrics.allowExternal Don't require client label for connections for metrics endpoint
    ## When set to false, only pods with the correct client label will have network access to the metrics port
    ##
    allowExternal: true
    ## @param networkPolicy.metrics.ingressNSMatchLabels Labels to match to allow traffic from other namespaces to metrics endpoint
    ## @param networkPolicy.metrics.ingressNSPodMatchLabels Pod labels to match to allow traffic from other namespaces to metrics endpoint
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## PodSecurityPolicy configuration
## ref: https://kubernetes.io/docs/concepts/policy/pod-security-policy/
##
podSecurityPolicy:
  ## @param podSecurityPolicy.create Whether to create a PodSecurityPolicy. WARNING: PodSecurityPolicy is deprecated in Kubernetes v1.21 or later, unavailable in v1.25 or later
  ##
  create: false
  ## @param podSecurityPolicy.enabled Enable PodSecurityPolicy's RBAC rules
  ##
  enabled: false
## RBAC configuration
##
rbac:
  ## @param rbac.create Specifies whether RBAC resources should be created
  ##
  create: false
  ## @param rbac.rules Custom RBAC rules to set
  ## e.g:
  ## rules:
  ##   - apiGroups:
  ##       - ""
  ##     resources:
  ##       - pods
  ##     verbs:
  ##       - get
  ##       - list
  ##
  rules: []
## ServiceAccount configuration
##
serviceAccount:
  ## @param serviceAccount.create Specifies whether a ServiceAccount should be created
  ##
  create: true
  ## @param serviceAccount.name The name of the ServiceAccount to use.
  ## If not set and create is true, a name is generated using the common.names.fullname template
  ##
  name: ""
  ## @param serviceAccount.automountServiceAccountToken Whether to auto mount the service account token
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/#use-the-default-service-account-to-access-the-api-server
  ##
  automountServiceAccountToken: false
  ## @param serviceAccount.annotations Additional custom annotations for the ServiceAccount
  ##
  annotations: {}
## Redis(R) Pod Disruption Budget configuration
## ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb/
## @param pdb DEPRECATED Please use `master.pdb` and `replica.pdb` values instead
##
pdb: {}
## TLS configuration
##
tls:
  ## @param tls.enabled Enable TLS traffic
  ##
  enabled: false
  ## @param tls.authClients Require clients to authenticate
  ##
  authClients: true
  ## @param tls.autoGenerated Enable autogenerated certificates
  ##
  autoGenerated: false
  ## @param tls.existingSecret The name of the existing secret that contains the TLS certificates
  ##
  existingSecret: ""
  ## @param tls.certificatesSecret DEPRECATED. Use existingSecret instead.
  ##
  certificatesSecret: ""
  ## @param tls.certFilename Certificate filename
  ##
  certFilename: ""
  ## @param tls.certKeyFilename Certificate Key filename
  ##
  certKeyFilename: ""
  ## @param tls.certCAFilename CA Certificate filename
  ##
  certCAFilename: ""
  ## @param tls.dhParamsFilename File containing DH params (in order to support DH based ciphers)
  ##
  dhParamsFilename: ""
## @section Metrics Parameters
##
metrics:
  ## @param metrics.enabled Start a sidecar prometheus exporter to expose Redis(R) metrics
  ##
  enabled: false
  ## Bitnami Redis(R) Exporter image
  ## ref: https://hub.docker.com/r/bitnami/redis-exporter/tags/
  ## @param metrics.image.registry [default: REGISTRY_NAME] Redis(R) Exporter image registry
  ## @param metrics.image.repository [default: REPOSITORY_NAME/redis-exporter] Redis(R) Exporter image repository
  ## @skip metrics.image.tag Redis(R) Exporter image tag (immutable tags are recommended)
  ## @param metrics.image.digest Redis(R) Exporter image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param metrics.image.pullPolicy Redis(R) Exporter image pull policy
  ## @param metrics.image.pullSecrets Redis(R) Exporter image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/redis-exporter
    tag: 1.74.0-debian-12-r0
    digest: ""
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## @param metrics.containerPorts.http Metrics HTTP container port
  ##
  containerPorts:
    http: 9121
  ## Configure extra options for Redis(R) containers' liveness, readiness & startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
  ## @param metrics.startupProbe.enabled Enable startupProbe on Redis(R) replicas nodes
  ## @param metrics.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param metrics.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param metrics.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param metrics.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param metrics.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param metrics.livenessProbe.enabled Enable livenessProbe on Redis(R) replicas nodes
  ## @param metrics.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param metrics.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param metrics.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param metrics.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param metrics.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## @param metrics.readinessProbe.enabled Enable readinessProbe on Redis(R) replicas nodes
  ## @param metrics.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param metrics.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param metrics.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param metrics.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param metrics.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    initialDelaySeconds: 5
    periodSeconds: 10
    timeoutSeconds: 1
    successThreshold: 1
    failureThreshold: 3
  ## @param metrics.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param metrics.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param metrics.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param metrics.command Override default metrics container init command (useful when using custom images)
  ##
  command: []
  ## @param metrics.redisTargetHost A way to specify an alternative Redis(R) hostname
  ## Useful for certificate CN/SAN matching
  ##
  redisTargetHost: "localhost"
  ## @param metrics.extraArgs Extra arguments for Redis(R) exporter, for example:
  ## e.g.:
  ## extraArgs:
  ##   check-keys: myKey,myOtherKey
  ##
  extraArgs: {}
  ## @param metrics.extraEnvVars Array with extra environment variables to add to Redis(R) exporter
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param metrics.containerSecurityContext.enabled Enabled Redis(R) exporter containers' Security Context
  ## @param metrics.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param metrics.containerSecurityContext.runAsUser Set Redis(R) exporter containers' Security Context runAsUser
  ## @param metrics.containerSecurityContext.runAsGroup Set Redis(R) exporter containers' Security Context runAsGroup
  ## @param metrics.containerSecurityContext.runAsNonRoot Set Redis(R) exporter containers' Security Context runAsNonRoot
  ## @param metrics.containerSecurityContext.allowPrivilegeEscalation Set Redis(R) exporter containers' Security Context allowPrivilegeEscalation
  ## @param metrics.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context read-only root filesystem
  ## @param metrics.containerSecurityContext.seccompProfile.type Set Redis(R) exporter containers' Security Context seccompProfile
  ## @param metrics.containerSecurityContext.capabilities.drop Set Redis(R) exporter containers' Security Context capabilities to drop
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
  ## @param metrics.extraVolumes Optionally specify extra list of additional volumes for the Redis(R) metrics sidecar
  ##
  extraVolumes: []
  ## @param metrics.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Redis(R) metrics sidecar
  ##
  extraVolumeMounts: []
  ## Redis(R) exporter resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param metrics.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if metrics.resources is set (metrics.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param metrics.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## @param metrics.podLabels Extra labels for Redis(R) exporter pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param metrics.podAnnotations [object] Annotations for Redis(R) exporter pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9121"
  ## Redis(R) exporter service parameters
  ##
  service:
    ## @param metrics.service.enabled Create Service resource(s) for scraping metrics using PrometheusOperator ServiceMonitor, can be disabled when using a PodMonitor
    ##
    enabled: true
    ## @param metrics.service.type Redis(R) exporter service type
    ##
    type: ClusterIP
    ## @param metrics.service.ports.http Redis(R) exporter service port
    ##
    ports:
      http: 9121
    ## @param metrics.service.externalTrafficPolicy Redis(R) exporter service external traffic policy
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param metrics.service.extraPorts Extra ports to expose (normally used with the `sidecar` value)
    ##
    extraPorts: []
    ## @param metrics.service.loadBalancerIP Redis(R) exporter service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
    ##
    loadBalancerIP: ""
    ## @param metrics.service.loadBalancerClass exporter service Load Balancer class if service type is `LoadBalancer` (optional, cloud specific)
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerClass: ""
    ## @param metrics.service.loadBalancerSourceRanges Redis(R) exporter service Load Balancer sources
    ## https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g.
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param metrics.service.annotations Additional custom annotations for Redis(R) exporter service
    ##
    annotations: {}
    ## @param metrics.service.clusterIP Redis(R) exporter service Cluster IP
    ##
    clusterIP: ""
  ## Prometheus Service Monitor
  ## ref: https://github.com/coreos/prometheus-operator
  ##      https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
  ##
  serviceMonitor:
    ## @param metrics.serviceMonitor.port the service port to scrape metrics from
    ##
    port: http-metrics
    ## @param metrics.serviceMonitor.enabled Create ServiceMonitor resource(s) for scraping metrics using PrometheusOperator
    ##
    enabled: false
    ## @param metrics.serviceMonitor.namespace The namespace in which the ServiceMonitor will be created
    ##
    namespace: ""
    ## @param metrics.serviceMonitor.tlsConfig [object] TLS configuration used for scrape endpoints used by Prometheus
    ##
    tlsConfig: {}
    ## @param metrics.serviceMonitor.interval The interval at which metrics should be scraped
    ##
    interval: 30s
    ## @param metrics.serviceMonitor.scrapeTimeout The timeout after which the scrape is ended
    ##
    scrapeTimeout: ""
    ## @param metrics.serviceMonitor.relabelings Metrics RelabelConfigs to apply to samples before scraping.
    ##
    relabelings: []
    ## @skip metrics.serviceMonitor.relabellings DEPRECATED: Use `metrics.serviceMonitor.relabelings` instead.
    ##
    relabellings: []
    ## @param metrics.serviceMonitor.metricRelabelings Metrics RelabelConfigs to apply to samples before ingestion.
    ##
    metricRelabelings: []
    ## @param metrics.serviceMonitor.honorLabels Specify honorLabels parameter to add the scrape endpoint
    ##
    honorLabels: false
    ## @param metrics.serviceMonitor.additionalLabels Additional labels that can be used so ServiceMonitor resource(s) can be discovered by Prometheus
    ##
    additionalLabels: {}
    ## @param metrics.serviceMonitor.podTargetLabels Labels from the Kubernetes pod to be transferred to the created metrics
    ##
    podTargetLabels: []
    ## @param metrics.serviceMonitor.sampleLimit Limit of how many samples should be scraped from every Pod
    ##
    sampleLimit: false
    ## @param metrics.serviceMonitor.targetLimit Limit of how many targets should be scraped
    ##
    targetLimit: false
    ## @param metrics.serviceMonitor.additionalEndpoints  Additional endpoints to scrape (e.g sentinel)
    ##
    additionalEndpoints: []
    # uncomment in order to scrape sentinel metrics, also to in order distinguish between Sentinel and Redis container metrics
    # add metricRelabelings with label like app=redis to main redis pod-monitor port
    # - interval: "30s"
    #   path: "/scrape"
    #   port: "http-metrics"
    #   params:
    #     target: ["localhost:26379"]
    #   metricRelabelings:
    #     - targetLabel: "app"
    #       replacement: "sentinel"
  ## Prometheus Pod Monitor
  ## ref: https://github.com/coreos/prometheus-operator
  ##      https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#podmonitor
  ##
  podMonitor:
    ## @param metrics.podMonitor.port the pod port to scrape metrics from
    ##
    port: metrics
    ## @param metrics.podMonitor.enabled Create PodMonitor resource(s) for scraping metrics using PrometheusOperator
    ##
    enabled: false
    ## @param metrics.podMonitor.namespace The namespace in which the PodMonitor will be created
    ##
    namespace: ""
    ## @param metrics.podMonitor.tlsConfig [object] TLS configuration used for scrape endpoints used by Prometheus
    ##
    tlsConfig: {}
    ## @param metrics.podMonitor.interval The interval at which metrics should be scraped
    ##
    interval: 30s
    ## @param metrics.podMonitor.scrapeTimeout The timeout after which the scrape is ended
    ##
    scrapeTimeout: ""
    ## @param metrics.podMonitor.relabelings Metrics RelabelConfigs to apply to samples before scraping.
    ##
    relabelings: []
    ## @skip metrics.podMonitor.relabellings DEPRECATED: Use `metrics.podMonitor.relabelings` instead.
    ##
    relabellings: []
    ## @param metrics.podMonitor.metricRelabelings Metrics RelabelConfigs to apply to samples before ingestion.
    ##
    metricRelabelings: []
    # - targetLabel: "app"
    #   replacement: "redis"
    ## @param metrics.podMonitor.honorLabels Specify honorLabels parameter to add the scrape endpoint
    ##
    honorLabels: false
    ## @param metrics.podMonitor.additionalLabels Additional labels that can be used so PodMonitor resource(s) can be discovered by Prometheus
    ##
    additionalLabels: {}
    ## @param metrics.podMonitor.podTargetLabels Labels from the Kubernetes pod to be transferred to the created metrics
    ##
    podTargetLabels: []
    ## @param metrics.podMonitor.sampleLimit Limit of how many samples should be scraped from every Pod
    ##
    sampleLimit: false
    ## @param metrics.podMonitor.targetLimit Limit of how many targets should be scraped
    ##
    targetLimit: false
    ## @param metrics.podMonitor.additionalEndpoints  Additional endpoints to scrape (e.g sentinel)
    ##
    additionalEndpoints: []
    # - interval: "30s"
    #   path: "/scrape"
    #   port: "metrics"
    #   params:
    #     target: ["localhost:26379"]
    #   metricRelabelings:
    #     - targetLabel: "app"
    #       replacement: "sentinel"
  ## Custom PrometheusRule to be defined
  ## ref: https://github.com/coreos/prometheus-operator#customresourcedefinitions
  ##
  prometheusRule:
    ## @param metrics.prometheusRule.enabled Create a custom prometheusRule Resource for scraping metrics using PrometheusOperator
    ##
    enabled: false
    ## @param metrics.prometheusRule.namespace The namespace in which the prometheusRule will be created
    ##
    namespace: ""
    ## @param metrics.prometheusRule.additionalLabels Additional labels for the prometheusRule
    ##
    additionalLabels: {}
    ## @param metrics.prometheusRule.rules Custom Prometheus rules
    ## e.g:
    ## rules:
    ##   - alert: RedisDown
    ##     expr: redis_up{service="{{ template "common.names.fullname" . }}-metrics"} == 0
    ##     for: 2m
    ##     labels:
    ##       severity: error
    ##     annotations:
    ##       summary: Redis(R) instance {{ "{{ $labels.instance }}" }} down
    ##       description: Redis(R) instance {{ "{{ $labels.instance }}" }} is down
    ##    - alert: RedisMemoryHigh
    ##      expr: >
    ##        redis_memory_used_bytes{service="{{ template "common.names.fullname" . }}-metrics"} * 100
    ##        /
    ##        redis_memory_max_bytes{service="{{ template "common.names.fullname" . }}-metrics"}
    ##        > 90
    ##      for: 2m
    ##      labels:
    ##        severity: error
    ##      annotations:
    ##        summary: Redis(R) instance {{ "{{ $labels.instance }}" }} is using too much memory
    ##        description: |
    ##          Redis(R) instance {{ "{{ $labels.instance }}" }} is using {{ "{{ $value }}" }}% of its available memory.
    ##    - alert: RedisKeyEviction
    ##      expr: |
    ##        increase(redis_evicted_keys_total{service="{{ template "common.names.fullname" . }}-metrics"}[5m]) > 0
    ##      for: 1s
    ##      labels:
    ##        severity: error
    ##      annotations:
    ##        summary: Redis(R) instance {{ "{{ $labels.instance }}" }} has evicted keys
    ##        description: |
    ##          Redis(R) instance {{ "{{ $labels.instance }}" }} has evicted {{ "{{ $value }}" }} keys in the last 5 minutes.
    ##
    rules: []
## @section Init Container Parameters
##

## 'volumePermissions' init container parameters
## Changes the owner and group of the persistent volume mount point to runAsUser:fsGroup values
##   based on the *podSecurityContext/*containerSecurityContext parameters
##
volumePermissions:
  ## @param volumePermissions.enabled Enable init container that changes the owner/group of the PV mount point to `runAsUser:fsGroup`
  ##
  enabled: false
  ## OS Shell + Utility image
  ## ref: https://hub.docker.com/r/bitnami/os-shell/tags/
  ## @param volumePermissions.image.registry [default: REGISTRY_NAME] OS Shell + Utility image registry
  ## @param volumePermissions.image.repository [default: REPOSITORY_NAME/os-shell] OS Shell + Utility image repository
  ## @skip volumePermissions.image.tag OS Shell + Utility image tag (immutable tags are recommended)
  ## @param volumePermissions.image.digest OS Shell + Utility image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param volumePermissions.image.pullPolicy OS Shell + Utility image pull policy
  ## @param volumePermissions.image.pullSecrets OS Shell + Utility image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/os-shell
    tag: 12-debian-12-r46
    digest: ""
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## Init container's resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param volumePermissions.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if volumePermissions.resources is set (volumePermissions.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param volumePermissions.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Init container Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
  ## @param volumePermissions.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param volumePermissions.containerSecurityContext.runAsUser Set init container's Security Context runAsUser
  ## NOTE: when runAsUser is set to special value "auto", init container will try to chown the
  ##   data folder to auto-determined user&group, using commands: `id -u`:`id -G | cut -d" " -f2`
  ##   "auto" is especially useful for OpenShift which has scc with dynamic user ids (and 0 is not allowed)
  ##
  containerSecurityContext:
    seLinuxOptions: {}
    runAsUser: 0

  ## @param volumePermissions.extraEnvVars Array with extra environment variables to add to volume permissions init container.
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []

## Kubectl InitContainer
## used by Sentinel to update the isMaster label on the Redis(TM) pods
##
kubectl:
  ## Bitnami Kubectl image version
  ## ref: https://hub.docker.com/r/bitnami/kubectl/tags/
  ## @param kubectl.image.registry [default: REGISTRY_NAME] Kubectl image registry
  ## @param kubectl.image.repository [default: REPOSITORY_NAME/kubectl] Kubectl image repository
  ## @skip kubectl.image.tag Kubectl image tag (immutable tags are recommended), by default, using the current version
  ## @param kubectl.image.digest Kubectl image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param kubectl.image.pullPolicy Kubectl image pull policy
  ## @param kubectl.image.pullSecrets Kubectl pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/kubectl
    tag: 1.33.1-debian-12-r5
    digest: ""
    ## Specify a imagePullPolicy
    ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## @param kubectl.command kubectl command to execute
  ##
  command: ["/opt/bitnami/scripts/kubectl-scripts/update-master-label.sh"]
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param kubectl.containerSecurityContext.enabled Enabled kubectl containers' Security Context
  ## @param kubectl.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param kubectl.containerSecurityContext.runAsUser Set kubectl containers' Security Context runAsUser
  ## @param kubectl.containerSecurityContext.runAsGroup Set kubectl containers' Security Context runAsGroup
  ## @param kubectl.containerSecurityContext.runAsNonRoot Set kubectl containers' Security Context runAsNonRoot
  ## @param kubectl.containerSecurityContext.allowPrivilegeEscalation Set kubectl containers' Security Context allowPrivilegeEscalation
  ## @param kubectl.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context read-only root filesystem
  ## @param kubectl.containerSecurityContext.seccompProfile.type Set kubectl containers' Security Context seccompProfile
  ## @param kubectl.containerSecurityContext.capabilities.drop Set kubectl containers' Security Context capabilities to drop
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    seccompProfile:
      type: RuntimeDefault
    capabilities:
      drop: ["ALL"]
  ## Bitnami Kubectl resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param kubectl.resources.limits The resources limits for the kubectl containers
  ## @param kubectl.resources.requests The requested resources for the kubectl containers
  ##
  resources:
    limits: {}
    requests: {}

## init-sysctl container parameters
## used to perform sysctl operation to modify Kernel settings (needed sometimes to avoid warnings)
##
sysctl:
  ## @param sysctl.enabled Enable init container to modify Kernel settings
  ##
  enabled: false
  ## OS Shell + Utility image
  ## ref: https://hub.docker.com/r/bitnami/os-shell/tags/
  ## @param sysctl.image.registry [default: REGISTRY_NAME] OS Shell + Utility image registry
  ## @param sysctl.image.repository [default: REPOSITORY_NAME/os-shell] OS Shell + Utility image repository
  ## @skip sysctl.image.tag OS Shell + Utility image tag (immutable tags are recommended)
  ## @param sysctl.image.digest OS Shell + Utility image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param sysctl.image.pullPolicy OS Shell + Utility image pull policy
  ## @param sysctl.image.pullSecrets OS Shell + Utility image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/os-shell
    tag: 12-debian-12-r46
    digest: ""
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## @param sysctl.command Override default init-sysctl container command (useful when using custom images)
  ##
  command: []
  ## @param sysctl.mountHostSys Mount the host `/sys` folder to `/host-sys`
  ##
  mountHostSys: false
  ## Init container's resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param sysctl.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, micro, small, medium, large, xlarge, 2xlarge). This is ignored if sysctl.resources is set (sysctl.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param sysctl.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
## @section useExternalDNS Parameters
##
## @param useExternalDNS.enabled Enable various syntax that would enable external-dns to work.  Note this requires a working installation of `external-dns` to be usable.
## @param useExternalDNS.additionalAnnotations Extra annotations to be utilized when `external-dns` is enabled.
## @param useExternalDNS.annotationKey The annotation key utilized when `external-dns` is enabled. Setting this to `false` will disable annotations.
## @param useExternalDNS.suffix The DNS suffix utilized when `external-dns` is enabled.  Note that we prepend the suffix with the full name of the release.
##
useExternalDNS:
  enabled: false
  suffix: ""
  annotationKey: external-dns.alpha.kubernetes.io/
  additionalAnnotations: {}
