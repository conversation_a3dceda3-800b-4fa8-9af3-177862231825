{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.replica.serviceAccount.create (eq .Values.architecture "replication") (not .Values.sentinel.enabled) }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.replica.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "redis.replicaServiceAccountName" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if or .Values.replica.serviceAccount.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.replica.serviceAccount.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
{{- end }}
