{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.master.serviceAccount.create (or (not (eq .Values.architecture "replication")) (not .Values.sentinel.enabled)) }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.master.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "redis.masterServiceAccountName" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if or .Values.master.serviceAccount.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.master.serviceAccount.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
{{- end }}
