{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: Service
metadata:
  name: {{ include "postgresql.v1.primary.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $labels := .Values.commonLabels }}
  {{- if .Values.primary.service.labels }}
  {{- $labels = include "common.tplvalues.merge" ( dict "values" ( list .Values.primary.service.labels $labels ) "context" . ) }}
  {{- end }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: primary
  {{- if or .Values.commonAnnotations .Values.primary.service.annotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.primary.service.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.primary.service.type }}
  {{- if or (eq .Values.primary.service.type "LoadBalancer") (eq .Values.primary.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.primary.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.primary.service.type "LoadBalancer") (not (empty .Values.primary.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.primary.service.loadBalancerSourceRanges | toJson}}
  {{- end }}
  {{- if and (eq .Values.primary.service.type "LoadBalancer") (not (empty .Values.primary.service.loadBalancerClass)) }}
  loadBalancerClass: {{ .Values.primary.service.loadBalancerClass }}
  {{- end }}
  {{- if and (eq .Values.primary.service.type "LoadBalancer") (not (empty .Values.primary.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.primary.service.loadBalancerIP }}
  {{- end }}
  {{- if and .Values.primary.service.clusterIP (eq .Values.primary.service.type "ClusterIP") }}
  clusterIP: {{ .Values.primary.service.clusterIP }}
  {{- end }}
  {{- if .Values.primary.service.sessionAffinity }}
  sessionAffinity: {{ .Values.primary.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.primary.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.primary.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  ports:
    - name: tcp-postgresql
      port: {{ template "postgresql.v1.service.port" . }}
      targetPort: tcp-postgresql
      {{- if and (or (eq .Values.primary.service.type "NodePort") (eq .Values.primary.service.type "LoadBalancer")) (not (empty .Values.primary.service.nodePorts.postgresql)) }}
      nodePort: {{ .Values.primary.service.nodePorts.postgresql }}
      {{- else if eq .Values.primary.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.primary.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.primary.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.primary.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: primary
