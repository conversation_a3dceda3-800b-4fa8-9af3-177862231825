{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.passwordUpdateJob.enabled (include "postgresql.v1.createPreviousSecret" .) }}
{{- $customUser := include "postgresql.v1.username" . }}
{{- $postgresPassword := .Values.passwordUpdateJob.previousPasswords.postgresPassword }}
{{- $password := .Values.passwordUpdateJob.previousPasswords.password }}
{{- $replicationPassword := .Values.passwordUpdateJob.previousPasswords.replicationPassword }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-previous-secret" (include "postgresql.v1.chart.fullname" .) | trunc 63 | trimSuffix "-" }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/part-of: postgresql
  {{- $defaultAnnotations := dict "helm.sh/hook" "pre-upgrade" "helm.sh/hook-delete-policy" "hook-succeeded" }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.commonAnnotations $defaultAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
type: Opaque
data:
  {{- if .Values.auth.enablePostgresUser }}
  postgres-password: {{ required "The previous postgres password is required!" $postgresPassword | b64enc | quote }}
  {{- end }}
  {{- if and (not (empty $customUser)) (ne $customUser "postgres") }}
  password: {{ required "The previous user password is required!" $password | b64enc | quote }}
  {{- end }}
  {{- if or (eq .Values.architecture "replication") .Values.primary.standby.enabled }}
  replication-password: {{ required "The previous replication password is required!" $replicationPassword | b64enc | quote }}
  {{- end }}
{{- end }}
