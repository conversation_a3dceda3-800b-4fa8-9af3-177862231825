{{- /*
Copyright Broadcom, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.backup.enabled }}
{{- $customUser := include "postgresql.v1.username" . }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "postgresql.v1.primary.fullname" . }}-pgdumpall
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.backup.cronjob.labels .Values.commonLabels ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: pg_dumpall
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.backup.cronjob.annotations .Values.commonAnnotations ) "context" . ) }}
  {{- if $annotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  schedule: {{ quote .Values.backup.cronjob.schedule }}
  {{- if .Values.backup.cronjob.timezone }}
  timeZone: {{ .Values.backup.cronjob.timezone | quote }}
  {{- end }}
  concurrencyPolicy: {{ .Values.backup.cronjob.concurrencyPolicy }}
  failedJobsHistoryLimit: {{ .Values.backup.cronjob.failedJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ .Values.backup.cronjob.successfulJobsHistoryLimit }}
  {{- if .Values.backup.cronjob.startingDeadlineSeconds }}
  startingDeadlineSeconds: {{ .Values.backup.cronjob.startingDeadlineSeconds }}
  {{- end }}
  jobTemplate:
    spec:
      {{- if .Values.backup.cronjob.ttlSecondsAfterFinished }}
      ttlSecondsAfterFinished: {{ .Values.backup.cronjob.ttlSecondsAfterFinished }}
      {{- end }}
      template:
        metadata:
          labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 12 }}
            app.kubernetes.io/component: pg_dumpall
          {{- if $annotations }}
          annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 12 }}
          {{- end }}
        spec:
          {{- include "postgresql.v1.imagePullSecrets" . | nindent 10 }}
          {{- if .Values.backup.cronjob.nodeSelector }}
          nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.backup.cronjob.nodeSelector "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.backup.cronjob.tolerations }}
          tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.backup.cronjob.tolerations "context" $) | nindent 12 }}
          {{- end }}
          containers:
          - name: {{ include "postgresql.v1.primary.fullname" . }}-pgdumpall
            image: {{ include "postgresql.v1.image" . }}
            imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
            env:
              - name: PGUSER
              {{- if .Values.auth.enablePostgresUser }}
                value: postgres
              {{- else }}
                value: {{ $customUser | quote }}
              {{- end }}
              {{- if .Values.auth.usePasswordFiles }}
              - name:  PGPASSWORD_FILE
                value: {{ printf "/opt/bitnami/postgresql/secrets/%s" (include "postgresql.v1.adminPasswordKey" .) }}
              {{- else }}
              - name: PGPASSWORD
                valueFrom:
                  secretKeyRef:
                    name: {{ include "postgresql.v1.secretName" . }}
                    key: {{ include "postgresql.v1.adminPasswordKey" . }}
              {{- end }}
              - name: PGHOST
                value: {{ include "postgresql.v1.primary.fullname" . }}
              - name: PGPORT
                value: {{ include "postgresql.v1.service.port" . | quote }}
              - name: PGDUMP_DIR
                value: {{ .Values.backup.cronjob.storage.mountPath }}
              {{- if .Values.tls.enabled }}
              - name:  PGSSLROOTCERT
                {{- if .Values.tls.autoGenerated }}
                value: /tmp/certs/ca.crt
                {{- else }}
                value: {{ printf "/tmp/certs/%s" .Values.tls.certCAFilename }}
                {{- end }}
              {{- end }}
            command: {{- include "common.tplvalues.render" (dict "value" .Values.backup.cronjob.command "context" $) | nindent 14 }}
            volumeMounts:
              {{- if .Values.tls.enabled }}
              - name: raw-certificates
                mountPath: /tmp/certs
              {{- end }}
              {{- if .Values.backup.cronjob.storage.enabled }}
              - name: datadir
                mountPath: {{ .Values.backup.cronjob.storage.mountPath }}
                subPath: {{ .Values.backup.cronjob.storage.subPath }}
              {{- end }}
              - name: empty-dir
                mountPath: /tmp
                subPath: tmp-dir
              {{- if .Values.auth.usePasswordFiles }}
              - name: postgresql-password
                mountPath: /opt/bitnami/postgresql/secrets/
              {{- end }}
              {{- if .Values.backup.cronjob.extraVolumeMounts }}
              {{- include "common.tplvalues.render" (dict "value" .Values.backup.cronjob.extraVolumeMounts "context" $) | nindent 14 }}
              {{- end }}
            {{- if .Values.backup.cronjob.containerSecurityContext.enabled }}
            securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.backup.cronjob.containerSecurityContext "context" $) | nindent 14 }}
            {{- end }}
            {{- if .Values.backup.cronjob.resources }}
            resources: {{- toYaml .Values.backup.cronjob.resources | nindent 14 }}
            {{- else if ne .Values.backup.cronjob.resourcesPreset "none" }}
            resources: {{- include "common.resources.preset" (dict "type" .Values.backup.cronjob.resourcesPreset) | nindent 14 }}
            {{- end }}
          restartPolicy: {{ .Values.backup.cronjob.restartPolicy }}
          {{- if .Values.backup.cronjob.podSecurityContext.enabled }}
          securityContext:
            fsGroup: {{ .Values.backup.cronjob.podSecurityContext.fsGroup }}
          {{- end }}
          volumes:
            {{- if .Values.tls.enabled }}
            - name: raw-certificates
              secret:
                secretName: {{ include "postgresql.v1.tlsSecretName" . }}
            {{- end }}
            {{- if .Values.backup.cronjob.storage.enabled }}
            {{- if .Values.backup.cronjob.storage.existingClaim }}
            - name: datadir
              persistentVolumeClaim:
                claimName: {{ printf "%s" (tpl .Values.backup.cronjob.storage.existingClaim .) }}
            {{- else }}
            - name: datadir
              persistentVolumeClaim:
                claimName: {{ include "postgresql.v1.primary.fullname" . }}-pgdumpall
            {{- end }}
            {{- end }}
            - name: empty-dir
              emptyDir: {}
            {{- if .Values.auth.usePasswordFiles }}
            - name: postgresql-password
              secret:
                secretName: {{ include "postgresql.v1.secretName" . }}
            {{- end }}
            {{- if .Values.backup.cronjob.extraVolumes }}
            {{- include "common.tplvalues.render" ( dict "value" .Values.backup.cronjob.extraVolumes "context" $ ) | nindent 12 }}
            {{- end }}
{{- end }}
