---
# Source: maas-system/charts/postgresql/templates/primary/networkpolicy.yaml
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: test01-postgresql
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/version: 17.5.0
    helm.sh/chart: postgresql-16.7.10
    app.kubernetes.io/component: primary
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/instance: test01
      app.kubernetes.io/name: postgresql
      app.kubernetes.io/component: primary
  policyTypes:
    - Ingress
    - Egress
  egress:
    - {}
  ingress:
    - ports:
        - port: 5432
---
# Source: maas-system/charts/redis/templates/networkpolicy.yaml
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: test01-redis
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/instance: test01
      app.kubernetes.io/name: redis
  policyTypes:
    - Ingress
    - Egress
  egress:
    - {}
  ingress:
    # Allow inbound connections
    - ports:
        - port: 6379
        - port: 26379
---
# Source: maas-system/charts/postgresql/templates/primary/pdb.yaml
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: test01-postgresql
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/version: 17.5.0
    helm.sh/chart: postgresql-16.7.10
    app.kubernetes.io/component: primary
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: test01
      app.kubernetes.io/name: postgresql
      app.kubernetes.io/component: primary
---
# Source: maas-system/charts/redis/templates/sentinel/pdb.yaml
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: test01-redis-node
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
    app.kubernetes.io/component: node
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: test01
      app.kubernetes.io/name: redis
      app.kubernetes.io/component: node
---
# Source: maas-system/charts/postgresql/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: test01-postgresql
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/version: 17.5.0
    helm.sh/chart: postgresql-16.7.10
automountServiceAccountToken: false
---
# Source: maas-system/charts/redis/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: false
metadata:
  name: test01-redis
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
---
# Source: maas-system/charts/postgresql/templates/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: test01-postgresql
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/version: 17.5.0
    helm.sh/chart: postgresql-16.7.10
type: Opaque
data:
  postgres-password: "dTRZa1hPYkoxZA=="
  # We don't auto-generate LDAP password when it's not provided as we do for other passwords
---
# Source: maas-system/charts/redis/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: test01-redis
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
type: Opaque
data:
  redis-password: "VVNZRFhIZjNidQ=="
---
# Source: maas-system/charts/maas-admin-backend/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: test01-maas-admin-backend-config
  labels:
    helm.sh/chart: maas-admin-backend-0.1.2
    app.kubernetes.io/name: maas-admin-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.6"
    app.kubernetes.io/managed-by: Helm
data:
  DB_SCHEMA: admin
  LOGGING_LEVEL: INFO
  OTEL_EXPORTER_OTLP_ENDPOINT: http://alloy.monitoring.svc.cluster.local:4318
  OTEL_LOGS_EXPORTER: none
  OTEL_METRICS_EXPORTER: none
  SERVER_PORT: "8080"
  SSO_APP_ID: 67fdd7d2cfdef8ad44139ca0
  SSO_APP_SECRET: "80"
  SSO_ISSUER: https://bcpc7idcbgt0.authing.cn/67fdd7d2cfdef8ad44139ca0/oidc
  SSO_REDIRECT_URI: http://127.0.0.1:8080/callback
---
# Source: maas-system/charts/maas-backend/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: test01-maas-backend-config
  labels:
    helm.sh/chart: maas-backend-0.1.2
    app.kubernetes.io/name: maas-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.4"
    app.kubernetes.io/managed-by: Helm
data:
  BIREN_KUBE_CONFIG_PATH: /app/config/biren-kubeconfig
  DB_SCHEMA: mas
  LOGGING_LEVEL: INFO
  OTEL_EXPORTER_OTLP_ENDPOINT: http://alloy.monitoring.svc.cluster.local:4318
  OTEL_LOGS_EXPORTER: none
  OTEL_METRICS_EXPORTER: none
  SERVER_PORT: "8080"
  SMS_SIGN_NAME: 矩量无限
  SMS_TEMPLATE_CODE: SMS_480195354
  WHITE_LIST: 18519191328,13910766765,18850575164,18601089004,13371760072,18846042481,15253159870,13501186880,15911022052,18833004035,13521988877,13910122380,13521891006,16601323312,13621360482,13811101748,18601083205,13910122380,17896009760,18611489680,13601176512,18510957757,13811203653,13051005220,18611105886,13683503202,13436934959,13071131926,13116179785,13811409780
  XIMU_KUBE_CONFIG_PATH: /app/config/kubeconfig
  XIMU_SERVE_SCALE_IN: "80"
  XIMU_SERVE_SCALE_MULTIPLEX: "5"
  XIMU_SERVE_SCALE_OUT: "15"
  XIMU_SERVE_SCALE_STEPS: "2"
  XIMU_WORKSPACE: moments8
  XIMU_WORKSPACE_ID: c871e622-91d2-45cd-a85b-029273565a20
---
# Source: maas-system/charts/redis/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: test01-redis-configuration
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
data:
  redis.conf: |-
    # User-supplied common configuration:
    # Enable AOF https://redis.io/topics/persistence#append-only-file
    appendonly yes
    # Disable RDB persistence, AOF persistence already enabled.
    save ""
    # End of common configuration
  master.conf: |-
    dir /data
    # User-supplied master configuration:
    rename-command FLUSHDB ""
    rename-command FLUSHALL ""
    # End of master configuration
  replica.conf: |-
    dir /data
    # User-supplied replica configuration:
    rename-command FLUSHDB ""
    rename-command FLUSHALL ""
    # End of replica configuration
  users.acl: |-
  sentinel.conf: |-
    dir "/tmp"
    port 26379
    sentinel monitor mymaster test01-redis-node-0.test01-redis-headless.default.svc.cluster.local 6379 2
    sentinel down-after-milliseconds mymaster 60000
    sentinel failover-timeout mymaster 180000
    sentinel parallel-syncs mymaster 1
    # User-supplied sentinel configuration:
    # End of sentinel configuration
---
# Source: maas-system/charts/redis/templates/health-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: test01-redis-health
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
data:
  ping_readiness_local.sh: |-
    #!/bin/bash

    [[ -f $REDIS_PASSWORD_FILE ]] && export REDIS_PASSWORD="$(< "${REDIS_PASSWORD_FILE}")"
    [[ -n "$REDIS_PASSWORD" ]] && export REDISCLI_AUTH="$REDIS_PASSWORD"
    response=$(
      timeout -s 15 $1 \
      redis-cli \
        -h localhost \
        -p $REDIS_PORT \
        ping
    )
    if [ "$?" -eq "124" ]; then
      echo "Timed out"
      exit 1
    fi
    if [ "$response" != "PONG" ]; then
      echo "$response"
      exit 1
    fi
  ping_liveness_local.sh: |-
    #!/bin/bash

    [[ -f $REDIS_PASSWORD_FILE ]] && export REDIS_PASSWORD="$(< "${REDIS_PASSWORD_FILE}")"
    [[ -n "$REDIS_PASSWORD" ]] && export REDISCLI_AUTH="$REDIS_PASSWORD"
    response=$(
      timeout -s 15 $1 \
      redis-cli \
        -h localhost \
        -p $REDIS_PORT \
        ping
    )
    if [ "$?" -eq "124" ]; then
      echo "Timed out"
      exit 1
    fi
    responseFirstWord=$(echo $response | head -n1 | awk '{print $1;}')
    if [ "$response" != "PONG" ] && [ "$responseFirstWord" != "LOADING" ] && [ "$responseFirstWord" != "MASTERDOWN" ]; then
      echo "$response"
      exit 1
    fi
  ping_sentinel.sh: |-
    #!/bin/bash
    [[ -f $REDIS_PASSWORD_FILE ]] && export REDIS_PASSWORD="$(< "${REDIS_PASSWORD_FILE}")"
    [[ -n "$REDIS_PASSWORD" ]] && export REDISCLI_AUTH="$REDIS_PASSWORD"
    response=$(
      timeout -s 15 $1 \
      redis-cli \
        -h localhost \
        -p $REDIS_SENTINEL_PORT \
        ping
    )
    if [ "$?" -eq "124" ]; then
      echo "Timed out"
      exit 1
    fi
    if [ "$response" != "PONG" ]; then
      echo "$response"
      exit 1
    fi
  parse_sentinels.awk: |-
    /ip/ {FOUND_IP=1}
    /port/ {FOUND_PORT=1}
    /runid/ {FOUND_RUNID=1}
    !/ip|port|runid/ {
      if (FOUND_IP==1) {
        IP=$1; FOUND_IP=0;
      }
      else if (FOUND_PORT==1) {
        PORT=$1;
        FOUND_PORT=0;
      } else if (FOUND_RUNID==1) {
        printf "\nsentinel known-sentinel mymaster %s %s %s", IP, PORT, $0; FOUND_RUNID=0;
      }
    }
  ping_readiness_master.sh: |-
    #!/bin/bash

    [[ -f $REDIS_MASTER_PASSWORD_FILE ]] && export REDIS_MASTER_PASSWORD="$(< "${REDIS_MASTER_PASSWORD_FILE}")"
    [[ -n "$REDIS_MASTER_PASSWORD" ]] && export REDISCLI_AUTH="$REDIS_MASTER_PASSWORD"
    response=$(
      timeout -s 15 $1 \
      redis-cli \
        -h $REDIS_MASTER_HOST \
        -p $REDIS_MASTER_PORT_NUMBER \
        ping
    )
    if [ "$?" -eq "124" ]; then
      echo "Timed out"
      exit 1
    fi
    if [ "$response" != "PONG" ]; then
      echo "$response"
      exit 1
    fi
  ping_liveness_master.sh: |-
    #!/bin/bash

    [[ -f $REDIS_MASTER_PASSWORD_FILE ]] && export REDIS_MASTER_PASSWORD="$(< "${REDIS_MASTER_PASSWORD_FILE}")"
    [[ -n "$REDIS_MASTER_PASSWORD" ]] && export REDISCLI_AUTH="$REDIS_MASTER_PASSWORD"
    response=$(
      timeout -s 15 $1 \
      redis-cli \
        -h $REDIS_MASTER_HOST \
        -p $REDIS_MASTER_PORT_NUMBER \
        ping
    )
    if [ "$?" -eq "124" ]; then
      echo "Timed out"
      exit 1
    fi
    responseFirstWord=$(echo $response | head -n1 | awk '{print $1;}')
    if [ "$response" != "PONG" ] && [ "$responseFirstWord" != "LOADING" ]; then
      echo "$response"
      exit 1
    fi
  ping_readiness_local_and_master.sh: |-
    script_dir="$(dirname "$0")"
    exit_status=0
    "$script_dir/ping_readiness_local.sh" $1 || exit_status=$?
    "$script_dir/ping_readiness_master.sh" $1 || exit_status=$?
    exit $exit_status
  ping_liveness_local_and_master.sh: |-
    script_dir="$(dirname "$0")"
    exit_status=0
    "$script_dir/ping_liveness_local.sh" $1 || exit_status=$?
    "$script_dir/ping_liveness_master.sh" $1 || exit_status=$?
    exit $exit_status
---
# Source: maas-system/charts/redis/templates/scripts-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: test01-redis-scripts
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
data:
  start-node.sh: |
    #!/bin/bash

    . /opt/bitnami/scripts/libos.sh
    . /opt/bitnami/scripts/liblog.sh
    . /opt/bitnami/scripts/libvalidations.sh

    get_port() {
        hostname="$1"
        type="$2"

        port_var=$(echo "${hostname^^}_SERVICE_PORT_$type" | sed "s/-/_/g")
        port=${!port_var}

        if [ -z "$port" ]; then
            case $type in
                "SENTINEL")
                    echo 26379
                    ;;
                "REDIS")
                    echo 6379
                    ;;
            esac
        else
            echo $port
        fi
    }

    get_full_hostname() {
        hostname="$1"
        full_hostname="${hostname}.${HEADLESS_SERVICE}"
        echo "${full_hostname}"
    }

    REDISPORT=$(get_port "$HOSTNAME" "REDIS")

    HEADLESS_SERVICE="test01-redis-headless.default.svc.cluster.local"

    if [ -n "$REDIS_EXTERNAL_MASTER_HOST" ]; then
        REDIS_SERVICE="$REDIS_EXTERNAL_MASTER_HOST"
    else
        REDIS_SERVICE="test01-redis.default.svc.cluster.local"
    fi

    SENTINEL_SERVICE_PORT=$(get_port "test01-redis" "SENTINEL")

    redis_cli_command() {
        local timeout="${1:-0}"

        local args=("-h" "$REDIS_SERVICE" "-p" "$SENTINEL_SERVICE_PORT")
        local command="redis-cli"
        if is_boolean_yes "$REDIS_TLS_ENABLED"; then
            args+=("--tls" "--cert" "$REDIS_TLS_CERT_FILE" "--key" "$REDIS_TLS_KEY_FILE")
            [ -n "$REDIS_TLS_CA_FILE" ] && args+=("--cacert" "$REDIS_TLS_CA_FILE")
        fi
        if [ "$timeout" -gt 0 ]; then
            command="timeout $timeout $command"
        fi

        echo "REDISCLI_AUTH="\$REDIS_PASSWORD"  $command ${args[*]}"
    }

    validate_quorum() {
        quorum_info_command="$(redis_cli_command) sentinel master mymaster"
        info "about to run the command: $quorum_info_command"
        eval $quorum_info_command | grep -Fq "s_down"
    }

    trigger_manual_failover() {
        failover_command="$(redis_cli_command) sentinel failover mymaster"
        info "about to run the command: $failover_command"
        eval $failover_command
    }

    get_sentinel_master_info() {
        sentinel_info_command="$(redis_cli_command 90) sentinel get-master-addr-by-name mymaster"
        info "about to run the command: $sentinel_info_command"
        retry_while "eval $sentinel_info_command" 2 5
    }

    [[ -f $REDIS_PASSWORD_FILE ]] && export REDIS_PASSWORD="$(< "${REDIS_PASSWORD_FILE}")"
    [[ -f $REDIS_MASTER_PASSWORD_FILE ]] && export REDIS_MASTER_PASSWORD="$(< "${REDIS_MASTER_PASSWORD_FILE}")"

    # check if there is a master
    master_in_persisted_conf="$(get_full_hostname "$HOSTNAME")"
    master_port_in_persisted_conf="$REDIS_MASTER_PORT_NUMBER"
    master_in_sentinel="$(get_sentinel_master_info)"
    redisRetVal=$?

    if [[ -f /opt/bitnami/redis-sentinel/etc/sentinel.conf ]]; then
        master_in_persisted_conf="$(awk '/monitor/ {print $4}' /opt/bitnami/redis-sentinel/etc/sentinel.conf)"
        master_port_in_persisted_conf="$(awk '/monitor/ {print $5}' /opt/bitnami/redis-sentinel/etc/sentinel.conf)"
        info "Found previous master ${master_in_persisted_conf}:${master_port_in_persisted_conf} in /opt/bitnami/redis-sentinel/etc/sentinel.conf"
        debug "$(cat /opt/bitnami/redis-sentinel/etc/sentinel.conf | grep monitor)"
    fi

    if [[ -f /opt/bitnami/redis/mounted-etc/users.acl ]];then
        cp /opt/bitnami/redis/mounted-etc/users.acl /opt/bitnami/redis/etc/users.acl
    fi

    if [[ $redisRetVal -ne 0 ]]; then
        if [[ "$master_in_persisted_conf" == "$(get_full_hostname "$HOSTNAME")" ]]; then
            # Case 1: No active sentinel and in previous sentinel.conf we were the master --> MASTER
            info "Configuring the node as master"
            export REDIS_REPLICATION_MODE="master"
        else
            # Case 2: No active sentinel and in previous sentinel.conf we were not master --> REPLICA
            info "Configuring the node as replica"
            export REDIS_REPLICATION_MODE="replica"
            REDIS_MASTER_HOST=${master_in_persisted_conf}
            REDIS_MASTER_PORT_NUMBER=${master_port_in_persisted_conf}
        fi
    else
        # Fetches current master's host and port
        REDIS_SENTINEL_INFO=($(get_sentinel_master_info))
        info "Current master: REDIS_SENTINEL_INFO=(${REDIS_SENTINEL_INFO[0]},${REDIS_SENTINEL_INFO[1]})"
        REDIS_MASTER_HOST=${REDIS_SENTINEL_INFO[0]}
        REDIS_MASTER_PORT_NUMBER=${REDIS_SENTINEL_INFO[1]}

        if [[ "$REDIS_MASTER_HOST" == "$(get_full_hostname "$HOSTNAME")" ]]; then
            # Case 3: Active sentinel and master it is this node --> MASTER
            info "Configuring the node as master"
            export REDIS_REPLICATION_MODE="master"
        else
            # Case 4: Active sentinel and master is not this node --> REPLICA
            info "Configuring the node as replica"
            export REDIS_REPLICATION_MODE="replica"
        fi
    fi

    if [[ -n "$REDIS_EXTERNAL_MASTER_HOST" ]]; then
      REDIS_MASTER_HOST="$REDIS_EXTERNAL_MASTER_HOST"
      REDIS_MASTER_PORT_NUMBER="${REDIS_EXTERNAL_MASTER_PORT}"
    fi

    if [[ -f /opt/bitnami/redis/mounted-etc/replica.conf ]];then
        cp /opt/bitnami/redis/mounted-etc/replica.conf /opt/bitnami/redis/etc/replica.conf
    fi

    if [[ -f /opt/bitnami/redis/mounted-etc/redis.conf ]];then
        cp /opt/bitnami/redis/mounted-etc/redis.conf /opt/bitnami/redis/etc/redis.conf
    fi

    echo "" >> /opt/bitnami/redis/etc/replica.conf
    echo "replica-announce-port $REDISPORT" >> /opt/bitnami/redis/etc/replica.conf
    echo "replica-announce-ip $(get_full_hostname "$HOSTNAME")" >> /opt/bitnami/redis/etc/replica.conf
    ARGS=("--port" "${REDIS_PORT}")

    if [[ "$REDIS_REPLICATION_MODE" = "slave" ]] || [[ "$REDIS_REPLICATION_MODE" = "replica" ]]; then
        ARGS+=("--replicaof" "${REDIS_MASTER_HOST}" "${REDIS_MASTER_PORT_NUMBER}")
    fi
    ARGS+=("--requirepass" "${REDIS_PASSWORD}")
    ARGS+=("--masterauth" "${REDIS_MASTER_PASSWORD}")
    ARGS+=("--include" "/opt/bitnami/redis/etc/replica.conf")
    ARGS+=("--include" "/opt/bitnami/redis/etc/redis.conf")
    exec redis-server "${ARGS[@]}"

  start-sentinel.sh: |
    #!/bin/bash

    . /opt/bitnami/scripts/libos.sh
    . /opt/bitnami/scripts/libvalidations.sh
    . /opt/bitnami/scripts/libfile.sh

    HEADLESS_SERVICE="test01-redis-headless.default.svc.cluster.local"
    REDIS_SERVICE="test01-redis.default.svc.cluster.local"

    get_port() {
        hostname="$1"
        type="$2"

        port_var=$(echo "${hostname^^}_SERVICE_PORT_$type" | sed "s/-/_/g")
        port=${!port_var}

        if [ -z "$port" ]; then
            case $type in
                "SENTINEL")
                    echo 26379
                    ;;
                "REDIS")
                    echo 6379
                    ;;
            esac
        else
            echo $port
        fi
    }

    get_full_hostname() {
        hostname="$1"
        full_hostname="${hostname}.${HEADLESS_SERVICE}"
        echo "${full_hostname}"
    }

    SERVPORT=$(get_port "$HOSTNAME" "SENTINEL")
    REDISPORT=$(get_port "$HOSTNAME" "REDIS")
    SENTINEL_SERVICE_PORT=$(get_port "test01-redis" "SENTINEL")

    sentinel_conf_set() {
        local -r key="${1:?missing key}"
        local value="${2:-}"

        # Sanitize inputs
        value="${value//\\/\\\\}"
        value="${value//&/\\&}"
        value="${value//\?/\\?}"
        [[ "$value" = "" ]] && value="\"$value\""

        replace_in_file "/opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf" "^#*\s*${key} .*" "${key} ${value}" false
    }
    sentinel_conf_add() {
        echo $'\n'"$@" >> "/opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf"
    }
    host_id() {
        echo "$1" | openssl sha1 | awk '{print $2}'
    }
    get_sentinel_master_info() {
        if is_boolean_yes "$REDIS_SENTINEL_TLS_ENABLED"; then
            sentinel_info_command="REDISCLI_AUTH="\$REDIS_PASSWORD" timeout 90 redis-cli -h $REDIS_SERVICE -p $SENTINEL_SERVICE_PORT --tls --cert ${REDIS_SENTINEL_TLS_CERT_FILE} --key ${REDIS_SENTINEL_TLS_KEY_FILE} --cacert ${REDIS_SENTINEL_TLS_CA_FILE} sentinel get-master-addr-by-name mymaster"
        else
            sentinel_info_command="REDISCLI_AUTH="\$REDIS_PASSWORD" timeout 90 redis-cli -h $REDIS_SERVICE -p $SENTINEL_SERVICE_PORT sentinel get-master-addr-by-name mymaster"
        fi
        info "about to run the command: $sentinel_info_command"
        retry_while "eval $sentinel_info_command" 2 5
    }

    [[ -f $REDIS_PASSWORD_FILE ]] && export REDIS_PASSWORD="$(< "${REDIS_PASSWORD_FILE}")"

    master_in_persisted_conf="$(get_full_hostname "$HOSTNAME")"

    if [[ -f /opt/bitnami/redis-sentinel/etc/sentinel.conf ]]; then
        master_in_persisted_conf="$(awk '/monitor/ {print $4}' /opt/bitnami/redis-sentinel/etc/sentinel.conf)"
        info "Found previous master $master_in_persisted_conf in /opt/bitnami/redis-sentinel/etc/sentinel.conf"
        debug "$(cat /opt/bitnami/redis-sentinel/etc/sentinel.conf | grep monitor)"
    fi
    REDIS_SENTINEL_INFO=($(get_sentinel_master_info))
    if [ "$?" -eq "0" ]; then
        # current master's host and port obtained from other Sentinel
        info "printing REDIS_SENTINEL_INFO=(${REDIS_SENTINEL_INFO[0]},${REDIS_SENTINEL_INFO[1]})"
        REDIS_MASTER_HOST=${REDIS_SENTINEL_INFO[0]}
        REDIS_MASTER_PORT_NUMBER=${REDIS_SENTINEL_INFO[1]}
    else
        REDIS_MASTER_HOST="$master_in_persisted_conf"
        REDIS_MASTER_PORT_NUMBER="$REDISPORT"
    fi
    if [[ "$REDIS_MASTER_HOST" == "$(get_full_hostname "$HOSTNAME")" ]]; then
        export REDIS_REPLICATION_MODE="master"
    else
        export REDIS_REPLICATION_MODE="replica"
    fi

    if [[ -n "$REDIS_EXTERNAL_MASTER_HOST" ]]; then
      REDIS_MASTER_HOST="$REDIS_EXTERNAL_MASTER_HOST"
      REDIS_MASTER_PORT_NUMBER="${REDIS_EXTERNAL_MASTER_PORT}"
    fi

    # To prevent incomplete configuration and as the redis container accesses /opt/bitnami/redis-sentinel/etc/sentinel.conf
    # as well, prepare the new config in `prepare-sentinel.conf` and move it atomically to the ultimate destination when it is complete.
    cp /opt/bitnami/redis-sentinel/mounted-etc/sentinel.conf /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf
    printf "\nsentinel auth-pass %s %s" "mymaster" "$REDIS_PASSWORD" >> /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf
    printf "\nrequirepass %s" "$REDIS_PASSWORD" >> /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf
    printf "\nsentinel myid %s" "$(host_id "$HOSTNAME")" >> /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf

    if [[ -z "$REDIS_MASTER_HOST" ]] || [[ -z "$REDIS_MASTER_PORT_NUMBER" ]]
    then
        # Prevent incorrect configuration to be written to sentinel.conf
        error "Redis master host is configured incorrectly (host: $REDIS_MASTER_HOST, port: $REDIS_MASTER_PORT_NUMBER)"
        exit 1
    fi
    sentinel_conf_set "sentinel monitor" "mymaster "$REDIS_MASTER_HOST" "$REDIS_MASTER_PORT_NUMBER" 2"

    add_known_sentinel() {
        hostname="$1"
        ip="$2"
        if [[ -n "$hostname" && -n "$ip" && "$hostname" != "$HOSTNAME" ]]; then
            sentinel_conf_add "sentinel known-sentinel mymaster $(get_full_hostname "$hostname") $(get_port "$hostname" "SENTINEL") $(host_id "$hostname")"
        fi
    }

    add_known_replica() {
        hostname="$1"
        ip="$2"
        if [[ -n "$ip" && "$(get_full_hostname "$hostname")" != "$REDIS_MASTER_HOST" ]]; then
            sentinel_conf_add "sentinel known-replica mymaster $(get_full_hostname "$hostname") $(get_port "$hostname" "REDIS")"
        fi
    }

    add_known_sentinel_public_ip() {
        hostname="$1"
        ip="$2"
        sentinel_conf_add "sentinel known-sentinel mymaster $ip $(get_port "$hostname" "SENTINEL") $(host_id "$hostname")"
    }

    add_known_replica_public_ip() {
        hostname="$1"
        ip="$2"
        sentinel_conf_add "sentinel known-replica mymaster $ip $(get_port "$hostname" "REDIS")"
    }

    for node in $(seq 0 $((3-1))); do
        hostname="test01-redis-node-$node"
        ip="$(getent hosts "$hostname.$HEADLESS_SERVICE" | awk '{ print $1 }')"
        add_known_sentinel "$hostname" "$ip"
        add_known_replica "$hostname" "$ip"
    done

    echo "" >> /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf
    echo "sentinel announce-hostnames yes" >> /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf
    echo "sentinel resolve-hostnames yes" >> /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf
    echo "sentinel announce-port $SERVPORT" >> /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf
    echo "sentinel announce-ip $(get_full_hostname "$HOSTNAME")" >> /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf
    mv /opt/bitnami/redis-sentinel/etc/prepare-sentinel.conf /opt/bitnami/redis-sentinel/etc/sentinel.conf
    exec redis-server /opt/bitnami/redis-sentinel/etc/sentinel.conf --sentinel
  prestop-sentinel.sh: |
    #!/bin/bash

    . /opt/bitnami/scripts/libvalidations.sh
    . /opt/bitnami/scripts/libos.sh

    HEADLESS_SERVICE="test01-redis-headless.default.svc.cluster.local"

    get_full_hostname() {
        hostname="$1"
        full_hostname="${hostname}.${HEADLESS_SERVICE}"
        echo "${full_hostname}"
    }

    run_sentinel_command() {
        if is_boolean_yes "$REDIS_SENTINEL_TLS_ENABLED"; then
            redis-cli -h "$REDIS_SERVICE" -p "$REDIS_SENTINEL_TLS_PORT_NUMBER" --tls --cert "$REDIS_SENTINEL_TLS_CERT_FILE" --key "$REDIS_SENTINEL_TLS_KEY_FILE" --cacert "$REDIS_SENTINEL_TLS_CA_FILE" sentinel "$@"
        else
            redis-cli -h "$REDIS_SERVICE" -p "$REDIS_SENTINEL_PORT" sentinel "$@"
        fi
    }
    sentinel_failover_finished() {
      REDIS_SENTINEL_INFO=($(run_sentinel_command get-master-addr-by-name "mymaster"))
      REDIS_MASTER_HOST="${REDIS_SENTINEL_INFO[0]}"
      [[ "$REDIS_MASTER_HOST" != "$(get_full_hostname $HOSTNAME)" ]]
    }

    REDIS_SERVICE="test01-redis.default.svc.cluster.local"

    # redis-cli automatically consumes credentials from the REDISCLI_AUTH variable
    [[ -n "$REDIS_PASSWORD" ]] && export REDISCLI_AUTH="$REDIS_PASSWORD"
    [[ -f "$REDIS_PASSWORD_FILE" ]] && export REDISCLI_AUTH="$(< "${REDIS_PASSWORD_FILE}")"

    if ! sentinel_failover_finished; then
        echo "I am the master pod and you are stopping me. Starting sentinel failover"
        if retry_while "sentinel_failover_finished" "20" 1; then
            echo "Master has been successfuly failed over to a different pod."
            exit 0
        else
            echo "Master failover failed"
            exit 1
        fi
    else
        exit 0
    fi
  prestop-redis.sh: |
    #!/bin/bash

    . /opt/bitnami/scripts/libvalidations.sh
    . /opt/bitnami/scripts/libos.sh

    run_redis_command() {
        local args=("-h" "127.0.0.1")
        if is_boolean_yes "$REDIS_TLS_ENABLED"; then
            args+=("-p" "$REDIS_TLS_PORT" "--tls" "--cert" "$REDIS_TLS_CERT_FILE" "--key" "$REDIS_TLS_KEY_FILE")
            [ -n "$REDIS_TLS_CA_FILE" ] && args+=("--cacert" "$REDIS_TLS_CA_FILE")
        else
            args+=("-p" "$REDIS_PORT")
        fi
        redis-cli "${args[@]}" "$@"
    }
    is_master() {
        REDIS_ROLE=$(run_redis_command role | head -1)
        [[ "$REDIS_ROLE" == "master" ]]
    }

    HEADLESS_SERVICE="test01-redis-headless.default.svc.cluster.local"

    get_full_hostname() {
        hostname="$1"
        full_hostname="${hostname}.${HEADLESS_SERVICE}"
        echo "${full_hostname}"
    }

    run_sentinel_command() {
        if is_boolean_yes "$REDIS_SENTINEL_TLS_ENABLED"; then
            redis-cli -h "$REDIS_SERVICE" -p "$REDIS_SENTINEL_TLS_PORT_NUMBER" --tls --cert "$REDIS_SENTINEL_TLS_CERT_FILE" --key "$REDIS_SENTINEL_TLS_KEY_FILE" --cacert "$REDIS_SENTINEL_TLS_CA_FILE" sentinel "$@"
        else
            redis-cli -h "$REDIS_SERVICE" -p "$REDIS_SENTINEL_PORT" sentinel "$@"
        fi
    }
    sentinel_failover_finished() {
        REDIS_SENTINEL_INFO=($(run_sentinel_command get-master-addr-by-name "mymaster"))
        REDIS_MASTER_HOST="${REDIS_SENTINEL_INFO[0]}"
        [[ "$REDIS_MASTER_HOST" != "$(get_full_hostname $HOSTNAME)" ]]
    }

    REDIS_SERVICE="test01-redis.default.svc.cluster.local"

    # redis-cli automatically consumes credentials from the REDISCLI_AUTH variable
    [[ -n "$REDIS_PASSWORD" ]] && export REDISCLI_AUTH="$REDIS_PASSWORD"
    [[ -f "$REDIS_PASSWORD_FILE" ]] && export REDISCLI_AUTH="$(< "${REDIS_PASSWORD_FILE}")"


    if is_master && ! sentinel_failover_finished; then
        echo "I am the master pod and you are stopping me. Pausing client connections."
        # Pausing client write connections to avoid data loss
        run_redis_command CLIENT PAUSE "22000" WRITE

        echo "Issuing failover"
        # if I am the master, issue a command to failover once
        run_sentinel_command failover "mymaster"
        echo "Waiting for sentinel to complete failover for up to 20s"
        retry_while "sentinel_failover_finished" "20" 1
    else
        exit 0
    fi
---
# Source: maas-system/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: test01-config
data:
  DB_HOST: test01-postgresql
  DB_PORT: "5432"
  DB_NAME: maas
  MAS_ADMIN_URL: http://test01-maas-admin-backend
  MAS_URL: http://test01-maas-backend
  REDIS_DATABASE: "0"
---
# Source: maas-system/charts/maas-admin-backend/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: test01-maas-admin-backend
  labels:
    helm.sh/chart: maas-admin-backend-0.1.2
    app.kubernetes.io/name: maas-admin-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.6"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 8080
    protocol: TCP
  - name: metrics
    port: 8082
    targetPort: 8082
    protocol: TCP
  selector:
    app.kubernetes.io/name: maas-admin-backend
    app.kubernetes.io/instance: test01
---
# Source: maas-system/charts/maas-admin-frontend/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: test01-maas-admin-frontend
  labels:
    helm.sh/chart: maas-admin-frontend-0.1.2
    app.kubernetes.io/name: maas-admin-frontend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.9"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  selector:
    app.kubernetes.io/name: maas-admin-frontend
    app.kubernetes.io/instance: test01
---
# Source: maas-system/charts/maas-backend/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: test01-maas-backend
  labels:
    helm.sh/chart: maas-backend-0.1.2
    app.kubernetes.io/name: maas-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.4"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 8080
    protocol: TCP
  - name: metrics
    port: 8082
    targetPort: 8082
    protocol: TCP
  selector:
    app.kubernetes.io/name: maas-backend
    app.kubernetes.io/instance: test01
---
# Source: maas-system/charts/maas-frontend/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: test01-maas-frontend
  labels:
    helm.sh/chart: maas-frontend-0.1.2
    app.kubernetes.io/name: maas-frontend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.6"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  selector:
    app.kubernetes.io/name: maas-frontend
    app.kubernetes.io/instance: test01
---
# Source: maas-system/charts/postgresql/templates/primary/svc-headless.yaml
apiVersion: v1
kind: Service
metadata:
  name: test01-postgresql-hl
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/version: 17.5.0
    helm.sh/chart: postgresql-16.7.10
    app.kubernetes.io/component: primary
  annotations:
spec:
  type: ClusterIP
  clusterIP: None
  # We want all pods in the StatefulSet to have their addresses published for
  # the sake of the other Postgresql pods even before they're ready, since they
  # have to be able to talk to each other in order to become ready.
  publishNotReadyAddresses: true
  ports:
    - name: tcp-postgresql
      port: 5432
      targetPort: tcp-postgresql
  selector:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/component: primary
---
# Source: maas-system/charts/postgresql/templates/primary/svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: test01-postgresql
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/version: 17.5.0
    helm.sh/chart: postgresql-16.7.10
    app.kubernetes.io/component: primary
spec:
  type: ClusterIP
  sessionAffinity: None
  ports:
    - name: tcp-postgresql
      port: 5432
      targetPort: tcp-postgresql
      nodePort: null
  selector:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/component: primary
---
# Source: maas-system/charts/redis/templates/headless-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: test01-redis-headless
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
spec:
  type: ClusterIP
  clusterIP: None
  publishNotReadyAddresses: true
  ports:
    - name: tcp-redis
      port: 6379
      targetPort: redis
    - name: tcp-sentinel
      port: 26379
      targetPort: redis-sentinel
  selector:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/name: redis
---
# Source: maas-system/charts/redis/templates/sentinel/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: test01-redis
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
    app.kubernetes.io/component: node
spec:
  type: ClusterIP
  sessionAffinity: None
  ports:
    - name: tcp-redis
      port: 6379
      targetPort: 6379
      nodePort: null
    - name: tcp-sentinel
      port: 26379
      targetPort: 26379
      nodePort: null
  selector:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: node
---
# Source: maas-system/charts/maas-admin-backend/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test01-maas-admin-backend
  labels:
    helm.sh/chart: maas-admin-backend-0.1.2
    app.kubernetes.io/name: maas-admin-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.6"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-admin-backend
    app.kubernetes.io/part-of: maas-system
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: maas-admin-backend
      app.kubernetes.io/instance: test01
  template:
    metadata:
      annotations:
        prometheus.io/path: /actuator/prometheus
        prometheus.io/port: "8082"
        prometheus.io/scheme: http
        prometheus.io/scrape: "true"
      labels:
        app.kubernetes.io/name: maas-admin-backend
        app.kubernetes.io/instance: test01
    spec:
      containers:
      - name: maas-admin-backend
        image: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/moments8/maas-admin-backend:latest"
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        - containerPort: 8082
        envFrom:
          - configMapRef:
              name: test01-config
          - secretRef:
              name: test01-db-admin-auth
          - secretRef:
              name: test01-jwt-keys
          - configMapRef:
              name: test01-maas-admin-backend-config
        env:
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: service.name=test01-maas-admin-backend,deployment.environment=prod,k8s.namespace.name=default
        - name: SPRING_PROFILES_ACTIVE
          value: prod
        resources:
          limits:
            cpu: 1000m
            memory: 2Gi
          requests:
            cpu: 100m
            memory: 2Gi
        startupProbe:
          httpGet:
            path: /ping
            port: 8080
          failureThreshold: 30
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /ping
            port: 8080
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ping
            port: 8080
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
---
# Source: maas-system/charts/maas-admin-frontend/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test01-maas-admin-frontend
  labels:
    helm.sh/chart: maas-admin-frontend-0.1.2
    app.kubernetes.io/name: maas-admin-frontend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.9"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-admin-frontend
    app.kubernetes.io/part-of: maas-system
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: maas-admin-frontend
      app.kubernetes.io/instance: test01
  template:
    metadata:
      annotations:
      labels:
        app.kubernetes.io/name: maas-admin-frontend
        app.kubernetes.io/instance: test01
    spec:
      containers:
      - name: maas-admin-frontend
        image: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/moments8/maas-admin-frontend:latest"
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        env:
        resources:
          limits:
            cpu: 500m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 3
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
---
# Source: maas-system/charts/maas-frontend/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test01-maas-frontend
  labels:
    helm.sh/chart: maas-frontend-0.1.2
    app.kubernetes.io/name: maas-frontend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.6"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-frontend
    app.kubernetes.io/part-of: maas-system
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: maas-frontend
      app.kubernetes.io/instance: test01
  template:
    metadata:
      annotations:
      labels:
        app.kubernetes.io/name: maas-frontend
        app.kubernetes.io/instance: test01
    spec:
      containers:
      - name: maas-frontend
        image: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/moments8/maas-frontend:latest"
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        env:
        resources:
          limits:
            cpu: 500m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 3
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - sleep 30 && nginx -s quit
---
# Source: maas-system/charts/maas-backend/templates/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: test01-maas-backend
  labels:
    helm.sh/chart: maas-backend-0.1.2
    app.kubernetes.io/name: maas-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.4"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-backend
    app.kubernetes.io/part-of: maas-system
spec:
  replicas: 1
  updateStrategy:
    rollingUpdate:
      partition: 0
    type: RollingUpdate
  serviceName: test01-maas-backend
  selector:
    matchLabels:
      app.kubernetes.io/name: maas-backend
      app.kubernetes.io/instance: test01
  template:
    metadata:
      annotations:
        prometheus.io/path: /actuator/prometheus
        prometheus.io/port: "8082"
        prometheus.io/scheme: http
        prometheus.io/scrape: "true"
      labels:
        app.kubernetes.io/name: maas-backend
        app.kubernetes.io/instance: test01
    spec:
      containers:
      - name: maas-backend
        image: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/moments8/maas-backend:latest"
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        - containerPort: 8082
        envFrom:
          - configMapRef:
              name: test01-config
          - secretRef:
              name: test01-db-maas-auth
          - secretRef:
              name: test01-jwt-keys
          - secretRef:
              name: test01-email-auth
          - configMapRef:
              name: test01-maas-backend-config
        env:
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: service.name=test01-maas-backend,deployment.environment=test,k8s.namespace.name=default
        - name: SPRING_PROFILES_ACTIVE
          value: test
        resources:
          limits:
            cpu: 1000m
            memory: 2Gi
          requests:
            cpu: 100m
            memory: 2Gi
        startupProbe:
          httpGet:
            path: /ping
            port: 8080
          failureThreshold: 30
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /ping
            port: 8080
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ping
            port: 8080
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
---
# Source: maas-system/charts/postgresql/templates/primary/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: test01-postgresql
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql
    app.kubernetes.io/version: 17.5.0
    helm.sh/chart: postgresql-16.7.10
    app.kubernetes.io/component: primary
spec:
  replicas: 1
  serviceName: test01-postgresql-hl
  updateStrategy:
    rollingUpdate: {}
    type: RollingUpdate
  selector:
    matchLabels:
      app.kubernetes.io/instance: test01
      app.kubernetes.io/name: postgresql
      app.kubernetes.io/component: primary
  template:
    metadata:
      name: test01-postgresql
      labels:
        app.kubernetes.io/instance: test01
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: postgresql
        app.kubernetes.io/version: 17.5.0
        helm.sh/chart: postgresql-16.7.10
        app.kubernetes.io/component: primary
    spec:
      serviceAccountName: test01-postgresql
      
      automountServiceAccountToken: false
      affinity:
        podAffinity:
          
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/instance: test01
                    app.kubernetes.io/name: postgresql
                    app.kubernetes.io/component: primary
                topologyKey: kubernetes.io/hostname
              weight: 1
        nodeAffinity:
          
      securityContext:
        fsGroup: 1001
        fsGroupChangePolicy: Always
        supplementalGroups: []
        sysctls: []
      hostNetwork: false
      hostIPC: false
      containers:
        - name: postgresql
          image: moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/standard/bitnami/postgresql:17.5.0-debian-12-r10
          imagePullPolicy: "IfNotPresent"
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: true
            runAsGroup: 1001
            runAsNonRoot: true
            runAsUser: 1001
            seLinuxOptions: {}
            seccompProfile:
              type: RuntimeDefault
          env:
            - name: BITNAMI_DEBUG
              value: "false"
            - name: POSTGRESQL_PORT_NUMBER
              value: "5432"
            - name: POSTGRESQL_VOLUME_DIR
              value: "/bitnami/postgresql"
            - name: PGDATA
              value: "/bitnami/postgresql/data"
            # Authentication
            - name: POSTGRES_PASSWORD_FILE
              value: /opt/bitnami/postgresql/secrets/postgres-admin-password
            - name: POSTGRES_DATABASE
              value: "maas"
            # LDAP
            - name: POSTGRESQL_ENABLE_LDAP
              value: "no"
            # TLS
            - name: POSTGRESQL_ENABLE_TLS
              value: "no"
            # Audit
            - name: POSTGRESQL_LOG_HOSTNAME
              value: "false"
            - name: POSTGRESQL_LOG_CONNECTIONS
              value: "false"
            - name: POSTGRESQL_LOG_DISCONNECTIONS
              value: "false"
            - name: POSTGRESQL_PGAUDIT_LOG_CATALOG
              value: "off"
            # Others
            - name: POSTGRESQL_CLIENT_MIN_MESSAGES
              value: "error"
            - name: POSTGRESQL_SHARED_PRELOAD_LIBRARIES
              value: "pgaudit"
          ports:
            - name: tcp-postgresql
              containerPort: 5432
          livenessProbe:
            failureThreshold: 6
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
            exec:
              command:
                - /bin/sh
                - -c
                - exec pg_isready -U "postgres" -d "dbname=maas" -h 127.0.0.1 -p 5432
          readinessProbe:
            failureThreshold: 6
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
            exec:
              command:
                - /bin/sh
                - -c
                - -e
                - |
                  exec pg_isready -U "postgres" -d "dbname=maas" -h 127.0.0.1 -p 5432
                  [ -f /opt/bitnami/postgresql/tmp/.initialized ] || [ -f /bitnami/postgresql/.initialized ]
          resources:
            limits:
              cpu: 2
              memory: 4Gi
            requests:
              cpu: 500m
              memory: 512Mi
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: empty-dir
              mountPath: /opt/bitnami/postgresql/conf
              subPath: app-conf-dir
            - name: empty-dir
              mountPath: /opt/bitnami/postgresql/tmp
              subPath: app-tmp-dir
            - name: custom-init-scripts-secret
              mountPath: /docker-entrypoint-initdb.d/secret
            - name: postgresql-password
              mountPath: /opt/bitnami/postgresql/secrets/
            - name: dshm
              mountPath: /dev/shm
            - name: data
              mountPath: /bitnami/postgresql
      volumes:
        - name: empty-dir
          emptyDir: {}
        - name: postgresql-password
          secret:
            secretName: test01-auth
        - name: custom-init-scripts-secret
          secret:
            secretName: test01-db-init-scripts
        - name: dshm
          emptyDir:
            medium: Memory
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: data
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: "10Gi"
        storageClassName: moments8-cnfs-nas
---
# Source: maas-system/charts/redis/templates/sentinel/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: test01-redis-node
  namespace: "default"
  labels:
    app.kubernetes.io/instance: test01
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: redis
    app.kubernetes.io/version: 8.0.2
    helm.sh/chart: redis-21.2.3
    app.kubernetes.io/component: node
spec:
  replicas: 3
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/instance: test01
      app.kubernetes.io/name: redis
      app.kubernetes.io/component: node
  serviceName: test01-redis-headless
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: test01
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: redis
        app.kubernetes.io/version: 8.0.2
        helm.sh/chart: redis-21.2.3
        app.kubernetes.io/component: node
      annotations:
        checksum/configmap: dc18e63c2a2d6b7204893e5acaf6f71b44548fa128401453f68759bd05a5ccc9
        checksum/health: 1eace71641fb5bd753e90012419e82c10b1180d694f91d83743b727a5aacca73
        checksum/scripts: 29ccdb773a22042ae974f168bc5f67c168a41ac21d7ec729d660749a3ddc1569
        checksum/secret: 8219e29f52aee7de74612f483496b775b47e5a810ebad21bd939b10e4bd151ac
    spec:
      
      automountServiceAccountToken: false
      securityContext:
        fsGroup: 1001
        fsGroupChangePolicy: Always
        supplementalGroups: []
        sysctls: []
      serviceAccountName: test01-redis
      affinity:
        podAffinity:
          
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/instance: test01
                    app.kubernetes.io/name: redis
                    app.kubernetes.io/component: node
                topologyKey: kubernetes.io/hostname
              weight: 1
        nodeAffinity:
          
      enableServiceLinks: true
      terminationGracePeriodSeconds: 30
      containers:
        - name: redis
          image: docker.io/bitnami/redis:8.0.2-debian-12-r3
          imagePullPolicy: "IfNotPresent"
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - -ec
                  - /opt/bitnami/scripts/start-scripts/prestop-redis.sh
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsGroup: 1001
            runAsNonRoot: true
            runAsUser: 1001
            seLinuxOptions: {}
            seccompProfile:
              type: RuntimeDefault
          command: ['/bin/bash', '-ec']
          args:
            - /opt/bitnami/scripts/start-scripts/start-node.sh
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: BITNAMI_DEBUG
              value: "false"
            - name: REDIS_MASTER_PORT_NUMBER
              value: "6379"
            - name: ALLOW_EMPTY_PASSWORD
              value: "no"
            - name: REDIS_PASSWORD_FILE
              value: "/opt/bitnami/redis/secrets/redis-password"
            - name: REDIS_MASTER_PASSWORD_FILE
              value: "/opt/bitnami/redis/secrets/redis-password"
            - name: REDIS_TLS_ENABLED
              value: "no"
            - name: REDIS_PORT
              value: "6379"
            - name: REDIS_SENTINEL_TLS_ENABLED
              value: "no"
            - name: REDIS_SENTINEL_PORT
              value: "26379"
            - name: REDIS_DATA_DIR
              value: /data
          ports:
            - name: redis
              containerPort: 6379
          startupProbe:
            failureThreshold: 22
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
            exec:
              command:
                - /bin/bash
                - -ec
                - "/health/ping_liveness_local.sh 5"
          livenessProbe:
            initialDelaySeconds: 20
            periodSeconds: 5
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 5
            exec:
              command:
                - /bin/bash
                - -ec
                - "/health/ping_liveness_local.sh 5"
          readinessProbe:
            initialDelaySeconds: 20
            periodSeconds: 5
            timeoutSeconds: 1
            successThreshold: 1
            failureThreshold: 5
            exec:
              command:
                - /bin/bash
                - -ec
                - "/health/ping_readiness_local.sh 1"
          resources:
            limits:
              cpu: 200m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 128Mi
          volumeMounts:
            - name: start-scripts
              mountPath: /opt/bitnami/scripts/start-scripts
            - name: health
              mountPath: /health
            - name: sentinel-data
              mountPath: /opt/bitnami/redis-sentinel/etc
            - name: redis-password
              mountPath: /opt/bitnami/redis/secrets/
            - name: redis-data
              mountPath: /data
            - name: config
              mountPath: /opt/bitnami/redis/mounted-etc
            - name: empty-dir
              mountPath: /opt/bitnami/redis/etc
              subPath: app-conf-dir
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
        - name: sentinel
          image: docker.io/bitnami/redis-sentinel:8.0.2-debian-12-r2
          imagePullPolicy: "IfNotPresent"
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - -ec
                  - /opt/bitnami/scripts/start-scripts/prestop-sentinel.sh
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsGroup: 1001
            runAsNonRoot: true
            runAsUser: 1001
            seLinuxOptions: {}
            seccompProfile:
              type: RuntimeDefault
          command: ['/bin/bash', '-ec']
          args:
            - /opt/bitnami/scripts/start-scripts/start-sentinel.sh
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: BITNAMI_DEBUG
              value: "false"
            - name: REDIS_PASSWORD_FILE
              value: "/opt/bitnami/redis/secrets/redis-password"
            - name: REDIS_SENTINEL_TLS_ENABLED
              value: "no"
            - name: REDIS_SENTINEL_PORT
              value: "26379"
          ports:
            - name: redis-sentinel
              containerPort: 26379
          startupProbe:
            failureThreshold: 22
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
            exec:
              command:
                - /bin/bash
                - -ec
                - "/health/ping_sentinel.sh 5"
          livenessProbe:
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
            exec:
              command:
                - /bin/bash
                - -ec
                - "/health/ping_sentinel.sh 5"
          readinessProbe:
            initialDelaySeconds: 20
            periodSeconds: 5
            timeoutSeconds: 1
            successThreshold: 1
            failureThreshold: 6
            exec:
              command:
                - /bin/bash
                - -ec
                - "/health/ping_sentinel.sh 1"
          resources:
            limits:
              cpu: 100m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          volumeMounts:
            - name: empty-dir
              mountPath: /tmp
              subPath: tmp-dir
            - name: start-scripts
              mountPath: /opt/bitnami/scripts/start-scripts
            - name: health
              mountPath: /health
            - name: sentinel-data
              mountPath: /opt/bitnami/redis-sentinel/etc
            - name: redis-password
              mountPath: /opt/bitnami/redis/secrets/
            - name: redis-data
              mountPath: /data
            - name: config
              mountPath: /opt/bitnami/redis-sentinel/mounted-etc
      volumes:
        - name: start-scripts
          configMap:
            name: test01-redis-scripts
            defaultMode: 0755
        - name: health
          configMap:
            name: test01-redis-health
            defaultMode: 0755
        - name: redis-password
          
          secret:
            secretName: test01-auth
            items:
            - key: redis-password
              path: redis-password
        - name: config
          configMap:
            name: test01-redis-configuration
        - name: empty-dir
          emptyDir: {}
  volumeClaimTemplates:
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: redis-data
        labels:
          app.kubernetes.io/instance: test01
          app.kubernetes.io/name: redis
          app.kubernetes.io/component: node
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: "1Gi"
        storageClassName: moments8-cnfs-nas
    - apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: sentinel-data
        labels:
          app.kubernetes.io/instance: test01
          app.kubernetes.io/name: redis
          app.kubernetes.io/component: node
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: "1Gi"
        storageClassName: moments8-cnfs-nas
---
# Source: maas-system/charts/maas-admin-backend/templates/cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: test01-maas-admin-backend-cronjob
  labels:
    helm.sh/chart: maas-admin-backend-0.1.2
    app.kubernetes.io/name: maas-admin-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.6"
    app.kubernetes.io/managed-by: Helm
spec:
  suspend: true
  schedule: "0 2 * * *"
  timeZone: Asia/Shanghai
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: Never
          containers:
          - name: maas-admin-backend-cronjob
            image: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/standard/tools:ubuntu2404"
            imagePullPolicy: Always
            command:
            - "bash"
            - "-c"
            - "#!/bin/bash\n# 检查环境变量是否设置\nif [ -z \"$DB_HOST\" ] || [ -z \"$DB_PORT\" ] || [ -z \"$DB_USERNAME\" ] || [ -z \"$DB_PASSWORD\" ] || [ -z \"$DB_NAME\" ]; then\n  echo \"错误: 请确保所有必要的数据库环境变量已设置\"\n  exit 1\nfi\n# 生成日期参数\nSTART_DATE=$(date -d \"yesterday\" +%Y-%m-%d)T00:00:00\nEND_DATE=$(date +%Y-%m-%d)T00:00:00\necho \"开始日期: $START_DATE\"\necho \"结束日期: $END_DATE\"\necho \"正在执行SQL存储过程...\"\n# 创建临时的SQL命令文件\nSQL_CMD=$(mktemp)\ncat << EOF > $SQL_CMD\nBEGIN;\n\nCALL admin.generate_user_active_count_daily(\nTIMESTAMP '$START_DATE',\nTIMESTAMP '$END_DATE'\n);\nCALL admin.generate_user_active_count_6hourly(\nTIMESTAMP '$START_DATE',\nTIMESTAMP '$END_DATE'\n);\nCALL admin.generate_user_active_count_hourly(\nTIMESTAMP '$START_DATE',\nTIMESTAMP '$END_DATE'\n);\n\n\nCALL admin.generate_user_new_count_daily(\nTIMESTAMP '$START_DATE',\nTIMESTAMP '$END_DATE'\n);\nCALL admin.generate_user_new_count_hourly(\nTIMESTAMP '$START_DATE',\nTIMESTAMP '$END_DATE'\n);\n\n\nCALL admin.generate_user_transaction_history_daily(\nTIMESTAMP '$START_DATE',\nTIMESTAMP '$END_DATE'\n);\nCALL admin.generate_user_transaction_history_hourly(\nTIMESTAMP '$START_DATE',\nTIMESTAMP '$END_DATE'\n);\n\n\nCALL admin.archive_user_transaction_history(\nTIMESTAMP '$START_DATE',\nTIMESTAMP '$END_DATE'\n);\n\nCALL admin.generate_user_violation_count_daily(\nTIMESTAMP '$START_DATE',\nTIMESTAMP '$END_DATE'\n);\n\n\nCOMMIT;\nEOF\n# 使用PGPASSWORD作为环境变量，不在命令行中显示\nexport DB_PASSWORD=$DB_PASSWORD\npsql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_NAME -f $SQL_CMD\n# 检查执行结果\nRESULT=$?\nif [ $RESULT -eq 0 ]; then\n  echo \"SQL存储过程执行成功\"\nelse\n  echo \"SQL存储过程执行失败\"\n  exit 1\nfi\nrm -f $SQL_CMD\nexit 0\n"
            envFrom:
              - configMapRef:
                  name: test01-config
              - secretRef:
                  name: test01-db-cronjob-auth
              - configMapRef:
                  name: test01-maas-admin-backend-config
---
# Source: maas-system/charts/maas-admin-backend/templates/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test01-maas-admin-backend
  labels:
    helm.sh/chart: maas-admin-backend-0.1.2
    app.kubernetes.io/name: maas-admin-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.6"
    app.kubernetes.io/managed-by: Helm
  annotations:
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80},{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/rewrite-target: /${1}
    alb.ingress.kubernetes.io/ssl-redirect: "true"
    alb.ingress.kubernetes.io/traffic-limit-qps: "500"
    alb.ingress.kubernetes.io/use-regex: "true"
    kubernetes.io/ingress.class: alb
spec:
  ingressClassName: alb
  rules:
    - host: "test01.kyle.moments8.com"
      http:
        paths:
          - path: /admin/api/(.*)
            pathType: Prefix
            backend:
              service:
                name: test01-maas-admin-backend
                port:
                  number: 80
---
# Source: maas-system/charts/maas-admin-frontend/templates/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test01-maas-admin-frontend
  labels:
    helm.sh/chart: maas-admin-frontend-0.1.2
    app.kubernetes.io/name: maas-admin-frontend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.9"
    app.kubernetes.io/managed-by: Helm
  annotations:
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80},{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/rewrite-target: /${1}
    alb.ingress.kubernetes.io/ssl-redirect: "true"
    alb.ingress.kubernetes.io/use-regex: "true"
    kubernetes.io/ingress.class: alb
spec:
  ingressClassName: alb
  rules:
    - host: "test01.kyle.moments8.com"
      http:
        paths:
          - path: /admin/(.*)
            pathType: Prefix
            backend:
              service:
                name: test01-maas-admin-frontend
                port:
                  number: 80
---
# Source: maas-system/charts/maas-backend/templates/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test01-maas-backend
  labels:
    helm.sh/chart: maas-backend-0.1.2
    app.kubernetes.io/name: maas-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.4"
    app.kubernetes.io/managed-by: Helm
  annotations:
    alb.ingress.kubernetes.io/backend-keepalive: "true"
    alb.ingress.kubernetes.io/idle-timeout: "300"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80},{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: "true"
    kubernetes.io/ingress.class: alb
spec:
  ingressClassName: alb
  rules:
    - host: "test01.kyle.moments8.com"
      http:
        paths:
          - path: /svc
            pathType: Prefix
            backend:
              service:
                name: test01-maas-backend
                port:
                  number: 80
          - path: /api
            pathType: Prefix
            backend:
              service:
                name: test01-maas-backend
                port:
                  number: 80
---
# Source: maas-system/charts/maas-frontend/templates/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test01-maas-frontend
  labels:
    helm.sh/chart: maas-frontend-0.1.2
    app.kubernetes.io/name: maas-frontend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.6"
    app.kubernetes.io/managed-by: Helm
  annotations:
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80},{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: "true"
    kubernetes.io/ingress.class: alb
spec:
  ingressClassName: alb
  rules:
    - host: "test01.kyle.moments8.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: test01-maas-frontend
                port:
                  number: 80
---
# Source: maas-system/charts/maas-admin-backend/templates/servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: test01-maas-admin-backend
  labels:
    helm.sh/chart: maas-admin-backend-0.1.2
    app.kubernetes.io/name: maas-admin-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.6"
    app.kubernetes.io/managed-by: Helm
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: maas-admin-backend
      app.kubernetes.io/instance: test01
  endpoints:
  - port: metrics
    path: /actuator/prometheus
    interval: 30s
    scrapeTimeout: 10s
---
# Source: maas-system/charts/maas-backend/templates/servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: test01-maas-backend
  labels:
    helm.sh/chart: maas-backend-0.1.2
    app.kubernetes.io/name: maas-backend
    app.kubernetes.io/instance: test01
    app.kubernetes.io/version: "prod.v20250630.4"
    app.kubernetes.io/managed-by: Helm
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: maas-backend
      app.kubernetes.io/instance: test01
  endpoints:
  - port: metrics
    path: /actuator/prometheus
    interval: 30s
    scrapeTimeout: 10s
---
# Source: maas-system/templates/jwt-keygen-job.yaml
# ServiceAccount for the job
apiVersion: v1
kind: ServiceAccount
metadata:
  name: test01-jwt-keygen
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-2"
    "helm.sh/hook-delete-policy": hook-succeeded
---
# Source: maas-system/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: test01-auth
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-10"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  postgres-admin-password: "SnY5NXdiZ2xYUXN0QW9LZQ=="
  postgres-database: "bWFhcw=="
  redis-password: "YnFmVmNGSzcxSkpBRDVWVw=="
---
# Source: maas-system/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: test01-db-init-scripts
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-10"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
stringData:
  01-init-users.sql: |
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE USER maas_user WITH PASSWORD 'YenSAurPBvfTAZN9';
    CREATE USER admin_user WITH PASSWORD 'QuERadmqJSUKjssR';
    CREATE USER cronjob_user WITH PASSWORD 'EP8jt9yZrAZDuZFV';
    CREATE SCHEMA mas AUTHORIZATION maas_user;
    CREATE SCHEMA admin AUTHORIZATION admin_user;
    GRANT USAGE ON SCHEMA mas TO admin_user;
    GRANT USAGE ON SCHEMA admin TO maas_user;
    GRANT USAGE, CREATE ON SCHEMA mas TO cronjob_user;
    GRANT USAGE, CREATE ON SCHEMA admin TO cronjob_user;
    GRANT SELECT ON ALL TABLES IN SCHEMA mas TO admin_user;
    GRANT SELECT ON ALL TABLES IN SCHEMA admin TO maas_user;
    GRANT ALL ON ALL TABLES IN SCHEMA mas TO cronjob_user;
    GRANT ALL ON ALL TABLES IN SCHEMA admin TO cronjob_user;
    ALTER DEFAULT PRIVILEGES FOR ROLE maas_user IN SCHEMA mas GRANT SELECT ON TABLES TO admin_user;
    ALTER DEFAULT PRIVILEGES FOR ROLE maas_user IN SCHEMA mas GRANT ALL ON TABLES TO cronjob_user;
    ALTER DEFAULT PRIVILEGES FOR ROLE admin_user IN SCHEMA admin GRANT SELECT ON TABLES TO maas_user;
    ALTER DEFAULT PRIVILEGES FOR ROLE admin_user IN SCHEMA admin GRANT ALL ON TABLES TO cronjob_user;
---
# Source: maas-system/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: test01-db-maas-auth
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-10"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  DB_PASSWORD: "WWVuU0F1clBCdmZUQVpOOQ=="
  REDIS_PASSWORD: "YnFmVmNGSzcxSkpBRDVWVw=="
  DB_USERNAME: "bWFhc191c2Vy"
  REDIS_SENTINEL_NODES: "dGVzdDAxLXJlZGlzLW5vZGUtMC50ZXN0MDEtcmVkaXMtaGVhZGxlc3M6MjYzNzksdGVzdDAxLXJlZGlzLW5vZGUtMS50ZXN0MDEtcmVkaXMtaGVhZGxlc3M6MjYzNzksdGVzdDAxLXJlZGlzLW5vZGUtMi50ZXN0MDEtcmVkaXMtaGVhZGxlc3M6MjYzNzk="
---
# Source: maas-system/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: test01-db-admin-auth
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-10"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  DB_PASSWORD: "UXVFUmFkbXFKU1VLanNzUg=="
  REDIS_PASSWORD: "YnFmVmNGSzcxSkpBRDVWVw=="
  DB_USERNAME: "YWRtaW5fdXNlcg=="
---
# Source: maas-system/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: test01-db-cronjob-auth
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-10"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  DB_PASSWORD: "RVA4anQ5eVpyQVpEdVpGVg=="
  DB_USERNAME: "Y3JvbmpvYl91c2Vy"
---
# Source: maas-system/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: test01-email-auth
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-10"
    "helm.sh/hook-delete-policy": before-hook-creation
type: Opaque
data:
  MAIL_HOST: "c210cGRtLmFsaXl1bi5jb20="
  MAIL_PORT: "ODA="
  MAIL_USERNAME:  "************************************"
  MAIL_PASSWORD: "TWFzRGV2MTIzNAo="
---
# Source: maas-system/templates/jwt-keygen-job.yaml
# ClusterRole for the job
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: test01-jwt-keygen
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-2" 
    "helm.sh/hook-delete-policy": hook-succeeded
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "create", "update", "patch"]
---
# Source: maas-system/templates/jwt-keygen-job.yaml
# RoleBinding for the job
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: test01-jwt-keygen
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-2"
    "helm.sh/hook-delete-policy": hook-succeeded
subjects:
- kind: ServiceAccount
  name: test01-jwt-keygen
  namespace: default
roleRef:
  kind: Role
  name: test01-jwt-keygen
  apiGroup: rbac.authorization.k8s.io
---
# Source: maas-system/templates/jwt-keygen-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: test01-jwt-keygen
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "-1"
    "helm.sh/hook-delete-policy": hook-succeeded
spec:
  template:
    spec:
      containers:
      - name: jwt-keygen
        image: moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com/standard/maven:3.9.9-eclipse-temurin-23-with-kubectl
        command:
        - bash
        - -c
        - |
          # 设置错误处理
          set -e

          # 检查并删除已存在的密钥
          if kubectl get secret test01-jwt-keys -n default 2>/dev/null; then
              echo "JWT keys already exist, skipping generation"
              exit 0
          fi

          # 重试机制的密钥生成函数
          generate_keys() {
              local attempt=1
              local max_attempts=3
              
              while [ $attempt -le $max_attempts ]; do
                  echo "Attempting to generate keys (attempt $attempt/$max_attempts)..."
                  
                  # 创建Java密钥生成器
                  cat > RSAKeyGen.java << 'EOF'
          import java.security.KeyPair;
          import java.security.KeyPairGenerator;
          import java.security.NoSuchAlgorithmException;
          import java.security.SecureRandom;
          import java.security.interfaces.RSAPrivateKey;
          import java.security.interfaces.RSAPublicKey;
          import java.util.Base64;

          public class RSAKeyGen {
              private static final String KEY_ALGORITHM = "RSA";
              private static final int KEY_SIZE = 2048;

              public static void main(String[] args) {
                  try {
                      // 使用SecureRandom确保随机性
                      SecureRandom secureRandom = new SecureRandom();
                      
                      // 初始化密钥对生成器
                      KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
                      keyPairGen.initialize(KEY_SIZE, secureRandom);

                      // 生成密钥对
                      KeyPair keyPair = keyPairGen.generateKeyPair();

                      // 获取公钥和私钥
                      RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
                      RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();

                      // 验证密钥不为空
                      if (publicKey == null || privateKey == null) {
                          System.err.println("Generated keys are null");
                          System.exit(1);
                      }

                      // 转换为Base64编码
                      String publicKeyBase64 = Base64.getEncoder().encodeToString(publicKey.getEncoded());
                      String privateKeyBase64 = Base64.getEncoder().encodeToString(privateKey.getEncoded());

                      // 验证编码后的密钥不为空
                      if (publicKeyBase64.isEmpty() || privateKeyBase64.isEmpty()) {
                          System.err.println("Encoded keys are empty");
                          System.exit(1);
                      }

                      // 输出密钥供shell脚本使用
                      System.out.println("PUBLIC_KEY=" + publicKeyBase64);
                      System.out.println("PRIVATE_KEY=" + privateKeyBase64);
                      
                  } catch (NoSuchAlgorithmException e) {
                      System.err.println("Error generating RSA key: " + e.getMessage());
                      System.exit(1);
                  } catch (Exception e) {
                      System.err.println("Unexpected error: " + e.getMessage());
                      System.exit(1);
                  }
              }
          }
          EOF
                  
                  # 编译Java程序
                  if ! javac RSAKeyGen.java; then
                      echo "Failed to compile Java program"
                      exit 1
                  fi
                  
                  # 运行Java程序生成密钥
                  if java RSAKeyGen > keys.env 2>&1; then
                      # 检查生成的密钥文件
                      if [ -s keys.env ]; then
                          # 读取并验证密钥
                          source keys.env
                          
                          # 验证密钥变量不为空
                          if [ -n "$PUBLIC_KEY" ] && [ -n "$PRIVATE_KEY" ]; then
                              echo "Keys generated successfully on attempt $attempt"
                              return 0
                          else
                              echo "Generated keys are empty on attempt $attempt"
                          fi
                      else
                          echo "Keys file is empty on attempt $attempt"
                      fi
                  else
                      echo "Failed to run Java program on attempt $attempt"
                      cat keys.env 2>/dev/null || true
                  fi
                  
                  # 清理失败的尝试
                  rm -f keys.env
                  
                  attempt=$((attempt + 1))
                  if [ $attempt -le $max_attempts ]; then
                      echo "Retrying in 2 seconds..."
                      sleep 2
                  fi
              done
              
              echo "Failed to generate valid keys after $max_attempts attempts"
              exit 1
          }

          # 生成密钥
          generate_keys

          # 验证密钥内容
          echo "Validating generated keys..."
          if [ ${#PUBLIC_KEY} -lt 100 ] || [ ${#PRIVATE_KEY} -lt 100 ]; then
              echo "Error: Generated keys appear to be too short"
              echo "Public key length: ${#PUBLIC_KEY}"
              echo "Private key length: ${#PRIVATE_KEY}"
              exit 1
          fi

          # 创建Kubernetes Secret
          echo "Creating Kubernetes secret..."
          if kubectl create secret generic test01-jwt-keys \
              --from-literal=JWT_PUBLIC_KEY="$PUBLIC_KEY" \
              --from-literal=JWT_PRIVATE_KEY="$PRIVATE_KEY" \
              -n default; then
              
              echo "JWT keys generated and stored successfully"
              
              # 验证创建的Secret
              echo "Verifying created secret..."
              if kubectl get secret test01-jwt-keys -n default -o jsonpath='{.data.JWT_PUBLIC_KEY}' | base64 -d | wc -c | grep -q -v '^0$'; then
                  echo "Secret verification passed"
              else
                  echo "Warning: Secret verification failed - keys may be empty"
                  exit 1
              fi
          else
              echo "Failed to create Kubernetes secret"
              exit 1
          fi
      restartPolicy: OnFailure
      serviceAccountName: test01-jwt-keygen
