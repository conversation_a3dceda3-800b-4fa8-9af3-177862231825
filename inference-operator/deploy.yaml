apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: inferenceservices.torin.moments8.com
spec:
  group: torin.moments8.com
  names:
    kind: InferenceService
    listKind: InferenceServiceList
    plural: inferenceservices
    shortNames:
    - is
    singular: inferenceservice
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Current phase of the inference service
      jsonPath: .status.phase
      name: Phase
      type: string
    - description: Total number of pods
      jsonPath: .status.replicas
      name: Replicas
      type: integer
    - description: Number of ready pods
      jsonPath: .status.readyReplicas
      name: Ready
      type: integer
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: InferenceService is the Schema for the inferenceservices API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: InferenceServiceSpec defines the desired state of InferenceService
            properties:
              concurrent:
                format: int32
                type: integer
              image:
                type: string
              resources:
                items:
                  properties:
                    gpuCount:
                      format: int32
                      type: integer
                    gpuType:
                      type: string
                    replicas:
                      format: int32
                      type: integer
                    vendor:
                      type: string
                  required:
                  - replicas
                  - vendor
                  type: object
                type: array
              restartPolicy:
                properties:
                  restartAt:
                    type: string
                type: object
            required:
            - image
            - resources
            type: object
          status:
            description: |-
              InferenceServiceStatus defines the observed state of InferenceService
              参照
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              containers:
                items:
                  properties:
                    State:
                      type: string
                    creationTimestamp:
                      format: date-time
                      type: string
                    gpuCount:
                      format: int32
                      type: integer
                    gpuType:
                      type: string
                    lastUpdateTimestamp:
                      format: date-time
                      type: string
                    message:
                      type: string
                    podName:
                      type: string
                    reason:
                      type: string
                    vendor:
                      type: string
                  required:
                  - State
                  - creationTimestamp
                  - gpuCount
                  - gpuType
                  - lastUpdateTimestamp
                  - podName
                  - vendor
                  type: object
                type: array
              phase:
                type: string
              readyReplicas:
                format: int32
                type: integer
              replicas:
                format: int32
                type: integer
              restartPolicy:
                properties:
                  restartAt:
                    type: string
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: inference-operator
  name: inference-operator
  namespace: torin-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: inference-operator
  name: inference-operator-leader-election-role
  namespace: torin-system
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: inference-operator
  name: inference-operator-inferenceservice-editor-role
rules:
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: inference-operator
  name: inference-operator-inferenceservice-viewer-role
rules:
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: inference-operator-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  - pods
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices/finalizers
  verbs:
  - update
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: inference-operator-metrics-auth-role
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: inference-operator-metrics-reader
rules:
- nonResourceURLs:
  - /metrics
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: inference-operator
  name: inference-operator-leader-election-rolebinding
  namespace: torin-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: inference-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: inference-operator
  namespace: torin-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: inference-operator
  name: inference-operator-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: inference-operator-manager-role
subjects:
- kind: ServiceAccount
  name: inference-operator
  namespace: torin-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: inference-operator-metrics-auth-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: inference-operator-metrics-auth-role
subjects:
- kind: ServiceAccount
  name: inference-operator
  namespace: torin-system
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: inference-operator
    control-plane: controller-manager
  name: inference-operator-metrics-service
  namespace: torin-system
spec:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: 8443
  selector:
    control-plane: controller-manager
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: inference-operator
    control-plane: controller-manager
  name: inference-operator
  namespace: torin-system
spec:
  replicas: 1
  selector:
    matchLabels:
      control-plane: controller-manager
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
    spec:
      containers:
      - args:
        - --metrics-bind-address=:8443
        - --leader-elect
        - --health-probe-bind-address=:8081
        command:
        - /manager
        image: controller:latest
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: manager
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      securityContext:
        runAsNonRoot: true
      serviceAccountName: inference-operator
      terminationGracePeriodSeconds: 10
