# Development values for inference-operator
# This is a YAML-formatted file for development deployment.

# Image configuration
image:
  registry: "harbor.intra.moments8.com"
  repository: "moments8/torin/inference-operator"
  tag: "dev.v20250918.1"
  pullPolicy: Always

# Deployment configuration
deployment:
  replicas: 1

# Container configuration
container:
  resources:
    limits:
      cpu: 500m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 64Mi

# Health check configuration
healthcheck:
  livenessProbe:
    httpGet:
      path: /healthz
      port: 8081
    initialDelaySeconds: 10
    periodSeconds: 10
  readinessProbe:
    httpGet:
      path: /readyz
      port: 8081
    initialDelaySeconds: 5
    periodSeconds: 5

# Common labels for development
commonLabels:
  environment: development
  tier: infrastructure

# Common annotations
commonAnnotations:
  deployment.kubernetes.io/revision: "1"
