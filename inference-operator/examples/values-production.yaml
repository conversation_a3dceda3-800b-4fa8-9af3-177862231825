# Production values for inference-operator
# This is a YAML-formatted file for production deployment.

# Image configuration
image:
  registry: "harbor.intra.moments8.com"
  repository: "moments8/torin/inference-operator"
  tag: "v1.0.0"
  pullPolicy: IfNotPresent

# Deployment configuration
deployment:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1

# Container configuration
container:
  resources:
    limits:
      cpu: 1000m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi

# Health check configuration
healthcheck:
  livenessProbe:
    httpGet:
      path: /healthz
      port: 8081
    initialDelaySeconds: 30
    periodSeconds: 30
    timeoutSeconds: 5
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /readyz
      port: 8081
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

# Node selector for control plane nodes
nodeSelector:
  node-role.kubernetes.io/control-plane: ""

# Tolerations for control plane nodes
tolerations:
  - key: node-role.kubernetes.io/control-plane
    operator: Exists
    effect: NoSchedule

# Affinity for high availability
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchLabels:
            app.kubernetes.io/name: inference-operator
        topologyKey: kubernetes.io/hostname

# Priority class for system critical workloads
priorityClassName: "system-cluster-critical"

# Common labels for production
commonLabels:
  environment: production
  tier: infrastructure

# Common annotations
commonAnnotations:
  deployment.kubernetes.io/revision: "1"
