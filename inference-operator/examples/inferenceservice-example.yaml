apiVersion: torin.moments8.com/v1alpha1
kind: InferenceService
metadata:
  name: example-inference
  namespace: torin-system
  labels:
    app: example-inference
    type: gpu-inference
spec:
  # Container image for the inference service
  image: "harbor.intra.moments8.com/moments8/torin/inference-service:latest"
  
  # Concurrent requests limit
  concurrent: 10
  
  # Resource requirements
  resources:
  - vendor: "nvidia"
    gpuType: "A100"
    gpuCount: 1
    replicas: 2
  - vendor: "nvidia"
    gpuType: "V100"
    gpuCount: 2
    replicas: 1
  
  # Restart policy
  restartPolicy:
    restartAt: "2024-01-01T00:00:00Z"
---
apiVersion: torin.moments8.com/v1alpha1
kind: InferenceService
metadata:
  name: cpu-inference
  namespace: torin-system
  labels:
    app: cpu-inference
    type: cpu-inference
spec:
  # Container image for CPU-based inference
  image: "harbor.intra.moments8.com/moments8/torin/cpu-inference:latest"
  
  # Concurrent requests limit
  concurrent: 50
  
  # CPU-only resource requirements
  resources:
  - vendor: "cpu"
    replicas: 3
