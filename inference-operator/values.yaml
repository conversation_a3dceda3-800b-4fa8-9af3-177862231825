# Default values for inference-operator
# This is a YAML-formatted file.
# Declare variables to be substituted into your templates.

# Global configuration
global:
  # Image pull secrets for private registries
  imagePullSecrets: []

# Image configuration
image:
  # Image registry
  registry: "harbor.intra.moments8.com"
  # Image repository
  repository: "moments8/torin/inference-operator"
  # Image tag (defaults to Chart appVersion if not set)
  tag: ""
  # Image pull policy
  pullPolicy: Always

# Deployment configuration
deployment:
  # Number of replicas
  replicas: 1
  # Deployment strategy
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1

# Container configuration
container:
  # Container name
  name: manager
  # Container command
  command: ["/manager"]
  # Container arguments
  args:
    - --metrics-bind-address=:8443
    - --leader-elect
    - --health-probe-bind-address=:8081
  # Container ports
  ports:
    - name: metrics
      containerPort: 8443
      protocol: TCP
    - name: health
      containerPort: 8081
      protocol: TCP
  # Resource limits and requests
  resources:
    limits:
      cpu: 500m
      memory: 128Mi
    requests:
      cpu: 10m
      memory: 64Mi
  # Security context
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL

# Pod configuration
pod:
  # Pod annotations
  annotations:
    kubectl.kubernetes.io/default-container: manager
  # Pod labels
  labels:
    control-plane: controller-manager
  # Pod security context
  securityContext:
    runAsNonRoot: true
  # Termination grace period
  terminationGracePeriodSeconds: 10

# Health check configuration
healthcheck:
  # Liveness probe
  livenessProbe:
    httpGet:
      path: /healthz
      port: 8081
    initialDelaySeconds: 15
    periodSeconds: 20
  # Readiness probe
  readinessProbe:
    httpGet:
      path: /readyz
      port: 8081
    initialDelaySeconds: 5
    periodSeconds: 10

# Service configuration
service:
  # Enable service creation
  enabled: true
  # Service name
  name: inference-operator-metrics-service
  # Service type
  type: ClusterIP
  # Service ports
  ports:
    - name: https
      port: 8443
      targetPort: 8443
      protocol: TCP
  # Service selector
  selector:
    control-plane: controller-manager

# ServiceAccount configuration
serviceAccount:
  # Enable ServiceAccount creation
  create: true
  # ServiceAccount name
  name: ""
  # ServiceAccount annotations
  annotations: {}

# RBAC configuration
rbac:
  # Enable RBAC creation
  create: true

# CRD configuration
crds:
  # Install CRDs
  install: true
  # Keep CRDs on uninstall
  keep: true

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Priority class name
priorityClassName: ""

# Additional labels for all resources
commonLabels: {}

# Additional annotations for all resources
commonAnnotations: {}
