# Inference Operator Helm Chart

A Helm chart for deploying the Inference Operator, a Kubernetes operator for managing inference services and GPU workloads.

## Description

The Inference Operator is a Kubernetes operator that manages InferenceService custom resources, providing automated deployment and lifecycle management for machine learning inference workloads. It supports both GPU and CPU-based inference services with flexible resource allocation and scaling capabilities.

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- Access to Harbor registry: `harbor.intra.moments8.com`

## Installation

### Basic Installation

```bash
helm install inference-operator ./inference-operator \
  --namespace torin-system \
  --create-namespace
```

### Production Installation

```bash
helm install inference-operator ./inference-operator \
  --namespace torin-system \
  --create-namespace \
  --values examples/values-production.yaml
```

### Development Installation

```bash
helm install inference-operator ./inference-operator \
  --namespace torin-system \
  --create-namespace \
  --values examples/values-development.yaml
```

## Configuration

### Key Configuration Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `image.registry` | Image registry | `harbor.intra.moments8.com` |
| `image.repository` | Image repository | `moments8/torin/inference-operator` |
| `image.tag` | Image tag | `""` (uses Chart appVersion) |
| `image.pullPolicy` | Image pull policy | `Always` |
| `deployment.replicas` | Number of replicas | `1` |
| `container.resources.limits.cpu` | CPU limit | `500m` |
| `container.resources.limits.memory` | Memory limit | `128Mi` |
| `container.resources.requests.cpu` | CPU request | `10m` |
| `container.resources.requests.memory` | Memory request | `64Mi` |
| `service.enabled` | Enable metrics service | `true` |
| `rbac.create` | Create RBAC resources | `true` |
| `crds.install` | Install CRDs | `true` |
| `crds.keep` | Keep CRDs on uninstall | `true` |

### RBAC Configuration

The chart creates the following RBAC resources:

- **ClusterRoles**:
  - `inference-operator-manager-role`: Main operator permissions
  - `inference-operator-inferenceservice-editor-role`: InferenceService CRUD operations
  - `inference-operator-inferenceservice-viewer-role`: InferenceService read-only access
  - `inference-operator-metrics-auth-role`: Metrics authentication
  - `inference-operator-metrics-reader`: Metrics access

- **Role**:
  - `inference-operator-leader-election-role`: Leader election permissions

- **ClusterRoleBindings**:
  - `inference-operator-manager-rolebinding`
  - `inference-operator-metrics-auth-rolebinding`

- **RoleBinding**:
  - `inference-operator-leader-election-rolebinding`

### Custom Resource Definition

The chart installs the `InferenceService` CRD with the following features:

- **API Version**: `torin.moments8.com/v1alpha1`
- **Kind**: `InferenceService`
- **Short Name**: `is`
- **Scope**: Namespaced

#### InferenceService Spec

```yaml
spec:
  image: string                    # Container image
  concurrent: integer              # Concurrent requests limit
  resources:                       # Resource requirements
  - vendor: string                 # Resource vendor (nvidia, cpu, etc.)
    gpuType: string               # GPU type (A100, V100, etc.)
    gpuCount: integer             # Number of GPUs
    replicas: integer             # Number of replicas
  restartPolicy:                  # Restart policy
    restartAt: string             # Restart timestamp
```

## Usage Examples

### Create an InferenceService

```bash
kubectl apply -f examples/inferenceservice-example.yaml
```

### Check InferenceService Status

```bash
# List all inference services
kubectl get inferenceservices -n torin-system

# Get detailed status
kubectl describe inferenceservice example-inference -n torin-system

# Watch for changes
kubectl get inferenceservices -n torin-system -w
```

### View Operator Logs

```bash
kubectl logs -f deployment/inference-operator -n torin-system
```

### Access Metrics

```bash
kubectl port-forward service/inference-operator-metrics-service 8443:8443 -n torin-system
curl -k https://localhost:8443/metrics
```

## Monitoring and Observability

### Health Checks

The operator provides health check endpoints:

- **Liveness Probe**: `/healthz` on port 8081
- **Readiness Probe**: `/readyz` on port 8081

### Metrics

Metrics are available at `/metrics` on port 8443 (HTTPS).

### Events

Monitor operator events:

```bash
kubectl get events -n torin-system --sort-by=.metadata.creationTimestamp
```

## Troubleshooting

### Common Issues

1. **Operator pods not starting**
   ```bash
   kubectl describe pod <pod-name> -n torin-system
   kubectl logs <pod-name> -n torin-system
   ```

2. **CRD not found**
   ```bash
   kubectl get crd inferenceservices.torin.moments8.com
   ```

3. **RBAC permission denied**
   ```bash
   kubectl auth can-i create inferenceservices --as=system:serviceaccount:torin-system:inference-operator
   ```

4. **InferenceService not reconciling**
   ```bash
   kubectl describe inferenceservice <name> -n <namespace>
   kubectl logs -f deployment/inference-operator -n torin-system
   ```

### Debug Mode

Enable debug logging by setting environment variables:

```yaml
container:
  env:
  - name: LOG_LEVEL
    value: "debug"
```

## Upgrade

```bash
helm upgrade inference-operator ./inference-operator \
  --namespace torin-system \
  --values your-values.yaml
```

## Uninstall

```bash
# Uninstall the operator
helm uninstall inference-operator --namespace torin-system

# Clean up CRDs (optional - will remove all InferenceService resources)
kubectl delete crd inferenceservices.torin.moments8.com

# Clean up namespace if no other resources
kubectl delete namespace torin-system
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test the chart thoroughly
5. Submit a pull request

## License

This project is licensed under the Apache 2.0 License.
