1. Get the application URL by running these commands:
{{- if .Values.service.enabled }}
  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "inference-operator.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:8080 to use your application"
  kubectl --namespace {{ .Release.Namespace }} port-forward $POD_NAME 8080:$CONTAINER_PORT
{{- end }}

2. Check the status of the inference operator:
  kubectl get deployment {{ include "inference-operator.fullname" . }} -n {{ .Release.Namespace }}

3. View the operator logs:
  kubectl logs -f deployment/{{ include "inference-operator.fullname" . }} -n {{ .Release.Namespace }}

4. Check if the CRD is installed:
  kubectl get crd inferenceservices.torin.moments8.com

5. Create an InferenceService example:
  kubectl apply -f - <<EOF
  apiVersion: torin.moments8.com/v1alpha1
  kind: InferenceService
  metadata:
    name: example-inference
    namespace: {{ .Release.Namespace }}
  spec:
    image: "nginx:latest"
    resources:
    - vendor: "nvidia"
      gpuType: "A100"
      gpuCount: 1
      replicas: 1
  EOF

6. Check InferenceService status:
  kubectl get inferenceservices -n {{ .Release.Namespace }}
  kubectl describe inferenceservice example-inference -n {{ .Release.Namespace }}

7. Access metrics (if metrics service is enabled):
{{- if .Values.service.enabled }}
  kubectl port-forward service/{{ .Values.service.name | default (printf "%s-metrics-service" (include "inference-operator.fullname" .)) }} 8443:8443 -n {{ .Release.Namespace }}
  # Then access https://localhost:8443/metrics
{{- end }}
