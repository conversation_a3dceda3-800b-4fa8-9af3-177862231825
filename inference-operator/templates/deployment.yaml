apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "inference-operator.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
    control-plane: controller-manager
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.deployment.replicas }}
  {{- if and .Values.deployment.strategy (not (empty .Values.deployment.strategy)) }}
  strategy:
    {{- toYaml .Values.deployment.strategy | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
      control-plane: controller-manager
  template:
    metadata:
      labels:
        {{- include "inference-operator.podLabels" . | nindent 8 }}
      {{- with (include "inference-operator.podAnnotations" .) }}
      annotations:
        {{- . | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "inference-operator.serviceAccountName" . }}
      {{- with .Values.pod.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.pod.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ . }}
      {{- end }}
      containers:
      - name: {{ .Values.container.name }}
        image: {{ include "inference-operator.image" . }}
        {{- if and .Values.image.pullPolicy (ne .Values.image.pullPolicy "") }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- end }}
        {{- with .Values.container.command }}
        command:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.container.args }}
        args:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- if .Values.container.ports }}
        ports:
          {{- toYaml .Values.container.ports | nindent 10 }}
        {{- end }}
        {{- with .Values.healthcheck.livenessProbe }}
        livenessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.healthcheck.readinessProbe }}
        readinessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.container.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.container.securityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
