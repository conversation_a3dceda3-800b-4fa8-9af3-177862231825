{{- if .Values.rbac.create }}
# Role for leader election
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "inference-operator.fullname" . }}-leader-election-role
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
# ClusterRole for inference service editor access
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "inference-operator.fullname" . }}-inferenceservice-editor-role
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
rules:
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices/status
  verbs:
  - get
---
# ClusterRole for inference service viewer access
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "inference-operator.fullname" . }}-inferenceservice-viewer-role
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
rules:
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices/status
  verbs:
  - get
---
# ClusterRole for manager permissions
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "inference-operator.fullname" . }}-manager-role
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  - pods
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices/finalizers
  verbs:
  - update
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices/status
  verbs:
  - get
  - patch
  - update
---
# ClusterRole for metrics authentication
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "inference-operator.fullname" . }}-metrics-auth-role
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
# ClusterRole for metrics reader access
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "inference-operator.fullname" . }}-metrics-reader
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
rules:
- nonResourceURLs:
  - /metrics
  verbs:
  - get
---
# RoleBinding for leader election
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "inference-operator.fullname" . }}-leader-election-rolebinding
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "inference-operator.fullname" . }}-leader-election-role
subjects:
- kind: ServiceAccount
  name: {{ include "inference-operator.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
---
# ClusterRoleBinding for manager permissions
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "inference-operator.fullname" . }}-manager-rolebinding
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "inference-operator.fullname" . }}-manager-role
subjects:
- kind: ServiceAccount
  name: {{ include "inference-operator.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
---
# ClusterRoleBinding for metrics authentication
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "inference-operator.fullname" . }}-metrics-auth-rolebinding
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "inference-operator.fullname" . }}-metrics-auth-role
subjects:
- kind: ServiceAccount
  name: {{ include "inference-operator.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
