{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.service.name | default (printf "%s-metrics-service" (include "inference-operator.fullname" .)) }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "inference-operator.labels" . | nindent 4 }}
    control-plane: controller-manager
  {{- with (include "inference-operator.annotations" .) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.service.ports }}
    - name: {{ .name }}
      port: {{ .port }}
      targetPort: {{ .targetPort }}
      protocol: {{ .protocol }}
    {{- end }}
  selector:
    {{- with .Values.service.selector }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
