# Image Warmup Operator Helm Chart

A Helm chart for deploying the Image Warmup Operator, a Kubernetes operator that pre-pulls container images on nodes to improve pod startup times.

## Description

The Image Warmup Operator allows you to pre-pull container images on specific nodes in your Kubernetes cluster. This is particularly useful for:

- **GPU workloads**: Pre-pulling large ML/AI images on GPU nodes
- **Critical applications**: Ensuring fast startup times for important services
- **Batch jobs**: Pre-warming images before running large-scale batch workloads
- **Node preparation**: Warming up images during node scaling events

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+

## Installation

### Install the chart

```bash
# Install with default values
helm upgrade --install image-warmup-operator oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts/image-warmup-operator --namespace torin-system --create-namespace

# Install with custom values
helm upgrade --install image-warmup-operator oci://moments8-acr-registry.cn-beijing.cr.aliyuncs.com/helm-charts/image-warmup-operator \
  --namespace torin-system \
  --create-namespace \
  --set image.tag=latest \
  --set replicaCount=2
```

### Install from local chart

```bash
helm upgrade --install image-warmup-operator ./charts/image-warmup-operator --namespace torin-system --create-namespace
```

## Usage

After installing the operator, you can create ImageWarmup resources to pre-pull images:

```yaml
apiVersion: torin.moments8.com/v1alpha1
kind: ImageWarmup
metadata:
  name: gpu-model-warmup
  namespace: default
spec:
  image: "harbor.intra.moments8.com/ml/pytorch:2.0-gpu"
  nodeSelector:
    gpu.enable: "true"
  concurrency: 3
  cleanupAfterSeconds: 300
```

### Check the status

```bash
# List all ImageWarmup resources
kubectl get imagewarmups --all-namespaces

# Get detailed status
kubectl describe imagewarmup gpu-model-warmup

# Check operator logs
kubectl logs -f deployment/image-warmup-operator-controller-manager -n torin-system
```

## Configuration

The following table lists the configurable parameters of the Image Warmup Operator chart and their default values.

### Global Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `global.imageRegistry` | Global Docker image registry | `""` |

### Image Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `image.registry` | Image registry | `harbor.intra.moments8.com` |
| `image.repository` | Image repository | `moments8/torin/image-warmup-operator` |
| `image.tag` | Image tag (overrides appVersion) | `""` |
| `image.pullPolicy` | Image pull policy | `IfNotPresent` |
| `imagePullSecrets` | Image pull secrets | `[]` |

### Deployment Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `replicaCount` | Number of replicas | `1` |
| `nameOverride` | Override chart name | `""` |
| `fullnameOverride` | Override full name | `""` |

### ServiceAccount Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `serviceAccount.create` | Create service account | `true` |
| `serviceAccount.automount` | Automount service account token | `true` |
| `serviceAccount.annotations` | Service account annotations | `{}` |
| `serviceAccount.name` | Service account name | `""` |

### Security Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `podSecurityContext.runAsNonRoot` | Run as non-root user | `true` |
| `securityContext.allowPrivilegeEscalation` | Allow privilege escalation | `false` |
| `securityContext.capabilities.drop` | Drop capabilities | `["ALL"]` |

### Resource Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `resources.requests.cpu` | CPU request | `10m` |
| `resources.requests.memory` | Memory request | `64Mi` |
| `resources.limits.cpu` | CPU limit | `500m` |
| `resources.limits.memory` | Memory limit | `128Mi` |

### Health Check Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `livenessProbe.enabled` | Enable liveness probe | `true` |
| `livenessProbe.httpGet.path` | Liveness probe path | `/healthz` |
| `livenessProbe.httpGet.port` | Liveness probe port | `8081` |
| `livenessProbe.initialDelaySeconds` | Initial delay | `15` |
| `livenessProbe.periodSeconds` | Period | `20` |
| `readinessProbe.enabled` | Enable readiness probe | `true` |
| `readinessProbe.httpGet.path` | Readiness probe path | `/readyz` |
| `readinessProbe.httpGet.port` | Readiness probe port | `8081` |
| `readinessProbe.initialDelaySeconds` | Initial delay | `5` |
| `readinessProbe.periodSeconds` | Period | `10` |

### Operator Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `operator.args.metricsBindAddress` | Metrics bind address | `:8443` |
| `operator.args.leaderElect` | Enable leader election | `true` |
| `operator.args.healthProbeBindAddress` | Health probe bind address | `:8081` |

### Metrics Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `metrics.enabled` | Enable metrics service | `true` |
| `metrics.service.type` | Service type | `ClusterIP` |
| `metrics.service.port` | Service port | `8443` |
| `metrics.service.targetPort` | Target port | `8443` |

### RBAC Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `rbac.create` | Create RBAC resources | `true` |

### Namespace Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `namespace.create` | Create namespace | `true` |
| `namespace.name` | Namespace name | `""` (uses release namespace) |

## Uninstalling the Chart

To uninstall/delete the `image-warmup-operator` deployment:

```bash
helm uninstall image-warmup-operator --namespace torin-system
```

The command removes all the Kubernetes components associated with the chart and deletes the release.

## Custom Resource Definition (CRD)

The chart includes the ImageWarmup CRD which defines the schema for image warmup operations. The CRD is automatically installed when you install the chart.

### ImageWarmup Spec

- `image`: The container image to pre-pull (required)
- `nodeSelector`: Node selector to target specific nodes (default: `gpu.enable: "true"`)
- `concurrency`: Maximum number of concurrent jobs (default: 3)
- `cleanupAfterSeconds`: Cleanup delay after completion (default: 300)

### ImageWarmup Status

The operator provides detailed status information including:
- Overall phase (Pending, InProgress, Succeeded, Failed)
- Per-node status with timing information
- Node counts (pending, in-progress, ready, failed, total)
- Validation errors if any

## Troubleshooting

### Common Issues

1. **Operator not starting**: Check the logs and ensure proper RBAC permissions
2. **ImageWarmup stuck in Pending**: Verify node selector matches available nodes
3. **Image pull failures**: Check image accessibility and pull secrets

### Debugging Commands

```bash
# Check operator status
kubectl get deployment image-warmup-operator-controller-manager -n torin-system

# View operator logs
kubectl logs -f deployment/image-warmup-operator-controller-manager -n torin-system

# Check ImageWarmup resources
kubectl get imagewarmups --all-namespaces -o wide

# Describe specific ImageWarmup
kubectl describe imagewarmup <name> -n <namespace>
```

## Contributing

Please read the contributing guidelines before submitting pull requests.

## License

This chart is licensed under the Apache 2.0 License.
