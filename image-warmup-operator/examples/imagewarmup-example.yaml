# Example ImageWarmup resource for pre-pulling a GPU-enabled PyTorch image
apiVersion: torin.moments8.com/v1alpha1
kind: ImageWarmup
metadata:
  name: pytorch-gpu-warmup
  namespace: default
spec:
  # Container image to pre-pull
  image: "harbor.intra.moments8.com/ml/pytorch:2.0-gpu"
  
  # Target nodes with GPU enabled
  nodeSelector:
    gpu.enable: "true"
  
  # Maximum number of concurrent image pulls
  concurrency: 3
  
  # Clean up jobs after 5 minutes (300 seconds)
  cleanupAfterSeconds: 300

---
# Example ImageWarmup for a standard application image
apiVersion: torin.moments8.com/v1alpha1
kind: ImageWarmup
metadata:
  name: nginx-warmup
  namespace: default
spec:
  # Standard nginx image
  image: "nginx:1.21"
  
  # Target all worker nodes
  nodeSelector:
    node-role.kubernetes.io/worker: ""
  
  # Higher concurrency for smaller image
  concurrency: 5
  
  # Clean up jobs after 2 minutes
  cleanupAfterSeconds: 120

---
# Example ImageWarmup for a large ML model
apiVersion: torin.moments8.com/v1alpha1
kind: ImageWarmup
metadata:
  name: large-model-warmup
  namespace: ml-workloads
spec:
  # Large ML model image
  image: "harbor.intra.moments8.com/ml/transformers:latest"
  
  # Target specific GPU nodes
  nodeSelector:
    gpu.enable: "true"
    gpu.type: "a100"
  
  # Lower concurrency for large image
  concurrency: 2
  
  # Keep jobs longer for debugging
  cleanupAfterSeconds: 600
