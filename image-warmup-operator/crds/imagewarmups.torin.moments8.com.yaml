apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: imagewarmups.torin.moments8.com
spec:
  group: torin.moments8.com
  names:
    kind: ImageWarmup
    listKind: ImageWarmupList
    plural: imagewarmups
    shortNames:
    - iw
    - iwu
    - warmup
    - image
    singular: imagewarmup
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.image
      name: Image
      type: string
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .status.nodesPending
      name: Pending
      priority: 1
      type: integer
    - jsonPath: .status.nodesInProgress
      name: InProgress
      priority: 1
      type: integer
    - jsonPath: .status.nodesReady
      name: Ready
      type: integer
    - jsonPath: .status.nodesFailed
      name: Failed
      priority: 1
      type: integer
    - jsonPath: .status.totalNodes
      name: Total
      type: integer
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .spec.concurrency
      name: Concurrency
      priority: 1
      type: integer
    - jsonPath: .spec.cleanupAfterSeconds
      name: CleanupAfter
      priority: 1
      type: integer
    - jsonPath: .status.completionTime
      name: CompletionTime
      priority: 1
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ImageWarmup is the Schema for the imagewarmups API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ImageWarmupSpec defines the desired state of ImageWarmup
            properties:
              cleanupAfterSeconds:
                default: 300
                description: CleanupAfterSeconds specifies how long to wait before
                  cleaning up jobs after completion
                format: int64
                minimum: 1
                type: integer
              concurrency:
                default: 3
                description: Concurrency is the maximum number of concurrent jobs
                  to run
                format: int32
                minimum: 1
                type: integer
              image:
                description: Image is the container image to be pre-pulled on nodes
                minLength: 1
                type: string
                x-kubernetes-validations:
                - message: Image field is immutable
                  rule: self == oldSelf
              nodeSelector:
                additionalProperties:
                  type: string
                default:
                  gpu.enable: "true"
                description: NodeSelector selects the nodes where the image should
                  be pulled
                type: object
                x-kubernetes-validations:
                - message: NodeSelector field is immutable
                  rule: self == oldSelf
            required:
            - image
            type: object
          status:
            description: ImageWarmupStatus defines the observed state of ImageWarmup
            properties:
              completionTime:
                description: CompletionTime represents when the image warmup operation
                  was completed
                format: date-time
                type: string
              conditions:
                description: Conditions represent the latest available observations
                  of the ImageWarmup's current state
                items:
                  description: ImageWarmupCondition describes the state of an ImageWarmup
                    operation
                  properties:
                    lastTransitionTime:
                      description: LastTransitionTime is the last time the condition
                        transitioned from one status to another
                      format: date-time
                      type: string
                    message:
                      description: Message is a human readable message indicating
                        details about the last transition
                      type: string
                    reason:
                      description: Reason is a brief CamelCase string that describes
                        why the condition is in this state
                      type: string
                    status:
                      description: Status of this condition, one of "True", "False",
                        "Unknown"
                      type: string
                    type:
                      description: Type of this condition
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              nodeStatuses:
                description: NodeStatuses contains the status for each node
                items:
                  description: NodeStatus defines the status of an individual node's
                    image pull operation
                  properties:
                    completeTime:
                      description: ImagePullCompleteTime is when the pulling image
                        completes
                      format: date-time
                      type: string
                    duration:
                      description: ImagePullDuration is when the node start to pull
                        image
                      format: int64
                      type: integer
                    lastUpdateTime:
                      description: LastUpdateTime is when the status was last updated
                      format: date-time
                      type: string
                    message:
                      description: Message provides additional information about the
                        current phase
                      type: string
                    nodeName:
                      description: NodeName is the name of the Kubernetes node
                      type: string
                    phase:
                      description: Phase indicates the current phase of the image
                        pull on this node
                      enum:
                      - Pending
                      - InProgress
                      - Succeeded
                      - Failed
                      type: string
                    podName:
                      description: PodName is the name of the pod doing the image
                        pull
                      type: string
                    startTime:
                      description: ImagePullStartTime is when the node start to pull
                        image
                      format: date-time
                      type: string
                  required:
                  - lastUpdateTime
                  - nodeName
                  - phase
                  - podName
                  type: object
                type: array
              nodesFailed:
                description: NodesFailed is the number of nodes that failed the image
                  pull
                format: int32
                type: integer
              nodesInProgress:
                description: NodesInProgress is the number of nodes currently pulling
                  the image
                format: int32
                type: integer
              nodesPending:
                description: NodesPending is the number of nodes waiting to start
                  the image pull
                format: int32
                type: integer
              nodesReady:
                description: NodesReady is the number of nodes that have successfully
                  completed the image pull
                format: int32
                type: integer
              phase:
                description: Phase represents the overall status of the ImageWarmup
                enum:
                - Pending
                - InProgress
                - Succeeded
                - Failed
                type: string
              startTime:
                description: StartTime represents when the image warmup operation
                  began
                format: date-time
                type: string
              totalNodes:
                description: TotalNodes is the total number of nodes targeted for
                  the image warmup
                format: int32
                type: integer
              validationErrors:
                description: ValidationErrors contains details about validation failures
                items:
                  description: ValidationError represents details about a validation
                    failure
                  properties:
                    existingInstanceName:
                      description: ResourceName is the name of the resource that caused
                        the validation error
                      type: string
                    existingInstanceNamespace:
                      description: ResourceNamespace is the namespace of the resource
                        that caused the validation error
                      type: string
                    image:
                      description: ConflictingValue is the value that caused the validation
                        to fail
                      type: string
                    message:
                      description: Message provides a description of the validation
                        error
                      type: string
                    timestamp:
                      description: Timestamp is the time when the validation error
                        occurred
                      format: date-time
                      type: string
                    type:
                      description: Type of the validation error
                      type: string
                  required:
                  - message
                  - timestamp
                  - type
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
