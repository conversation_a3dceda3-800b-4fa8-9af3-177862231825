1. Get the application URL by running these commands:
{{- if .Values.metrics.enabled }}
  export POD_NAME=$(kubectl get pods --namespace {{ include "image-warmup-operator.namespace" . }} -l "{{ include "image-warmup-operator.selectorLabels" . | replace ": " "=" | replace "\n" "," }}" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{ include "image-warmup-operator.namespace" . }} $POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:8080 to access the metrics endpoint"
  kubectl --namespace {{ include "image-warmup-operator.namespace" . }} port-forward $POD_NAME 8080:$CONTAINER_PORT
{{- end }}

2. Check the operator status:
  kubectl get deployment {{ include "image-warmup-operator.fullname" . }} -n {{ include "image-warmup-operator.namespace" . }}

3. View operator logs:
  kubectl logs -f deployment/{{ include "image-warmup-operator.fullname" . }} -n {{ include "image-warmup-operator.namespace" . }}

4. Create an ImageWarmup resource to test the operator:
  cat <<EOF | kubectl apply -f -
  apiVersion: torin.moments8.com/v1alpha1
  kind: ImageWarmup
  metadata:
    name: example-warmup
    namespace: {{ include "image-warmup-operator.namespace" . }}
  spec:
    image: nginx:latest
    nodeSelector:
      gpu.enable: "true"
    concurrency: 3
    cleanupAfterSeconds: 300
  EOF

5. Check ImageWarmup status:
  kubectl get imagewarmups -n {{ include "image-warmup-operator.namespace" . }}
  kubectl describe imagewarmup example-warmup -n {{ include "image-warmup-operator.namespace" . }}

6. List all ImageWarmup resources across all namespaces:
  kubectl get imagewarmups --all-namespaces

For more information about the Image Warmup Operator, visit:
https://github.com/moments8/image-warmup-operator
