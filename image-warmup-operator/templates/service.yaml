{{- if .Values.metrics.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "image-warmup-operator.metricsServiceName" . }}
  namespace: {{ include "image-warmup-operator.namespace" . }}
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
    control-plane: controller-manager
spec:
  type: {{ .Values.metrics.service.type }}
  ports:
  - name: {{ .Values.metrics.service.name }}
    port: {{ .Values.metrics.service.port }}
    protocol: {{ .Values.metrics.service.protocol }}
    targetPort: {{ .Values.metrics.service.targetPort }}
  selector:
    {{- include "image-warmup-operator.selectorLabels" . | nindent 4 }}
{{- end }}
