{{/*
Expand the name of the chart.
*/}}
{{- define "image-warmup-operator.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "image-warmup-operator.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "image-warmup-operator.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "image-warmup-operator.labels" -}}
helm.sh/chart: {{ include "image-warmup-operator.chart" . }}
{{ include "image-warmup-operator.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- with .Values.additionalLabels }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "image-warmup-operator.selectorLabels" -}}
app.kubernetes.io/name: {{ include "image-warmup-operator.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
control-plane: controller-manager
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "image-warmup-operator.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (printf "%s-controller-manager" (include "image-warmup-operator.fullname" .)) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the namespace to use
*/}}
{{- define "image-warmup-operator.namespace" -}}
{{- .Release.Namespace }}
{{- end }}

{{/*
Create the image name
*/}}
{{- define "image-warmup-operator.image" -}}
{{- $registry := .Values.global.imageRegistry | default .Values.image.registry }}
{{- $repository := .Values.image.repository }}
{{- $tag := .Values.image.tag | default .Chart.AppVersion }}
{{- if $registry }}
{{- printf "%s/%s:%s" $registry $repository $tag }}
{{- else }}
{{- printf "%s:%s" $repository $tag }}
{{- end }}
{{- end }}

{{/*
Create manager role name
*/}}
{{- define "image-warmup-operator.managerRoleName" -}}
{{- printf "%s-manager-role" (include "image-warmup-operator.fullname" .) }}
{{- end }}

{{/*
Create leader election role name
*/}}
{{- define "image-warmup-operator.leaderElectionRoleName" -}}
{{- printf "%s-leader-election-role" (include "image-warmup-operator.fullname" .) }}
{{- end }}

{{/*
Create metrics auth role name
*/}}
{{- define "image-warmup-operator.metricsAuthRoleName" -}}
{{- printf "%s-metrics-auth-role" (include "image-warmup-operator.fullname" .) }}
{{- end }}

{{/*
Create metrics reader role name
*/}}
{{- define "image-warmup-operator.metricsReaderRoleName" -}}
{{- printf "%s-metrics-reader" (include "image-warmup-operator.fullname" .) }}
{{- end }}

{{/*
Create imagewarmup editor role name
*/}}
{{- define "image-warmup-operator.imagewarmupEditorRoleName" -}}
{{- printf "%s-imagewarmup-editor-role" (include "image-warmup-operator.fullname" .) }}
{{- end }}

{{/*
Create imagewarmup viewer role name
*/}}
{{- define "image-warmup-operator.imagewarmupViewerRoleName" -}}
{{- printf "%s-imagewarmup-viewer-role" (include "image-warmup-operator.fullname" .) }}
{{- end }}

{{/*
Create metrics service name
*/}}
{{- define "image-warmup-operator.metricsServiceName" -}}
{{- printf "%s-metrics" (include "image-warmup-operator.fullname" .) | trunc 63 | trimSuffix "-" }}
{{- end }}
