{{- if .Values.rbac.create -}}
# Leader Election Role
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "image-warmup-operator.leaderElectionRoleName" . }}
  namespace: {{ include "image-warmup-operator.namespace" . }}
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
# ImageWarmup Editor Role
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "image-warmup-operator.imagewarmupEditorRoleName" . }}
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - torin.moments8.com
  resources:
  - imagewarmups
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - imagewarmups/status
  verbs:
  - get
---
# ImageWarmup Viewer Role
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "image-warmup-operator.imagewarmupViewerRoleName" . }}
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - torin.moments8.com
  resources:
  - imagewarmups
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - imagewarmups/status
  verbs:
  - get
---
# Manager Role
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "image-warmup-operator.managerRoleName" . }}
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - batch
  resources:
  - jobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - imagewarmups
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - imagewarmups/finalizers
  verbs:
  - update
- apiGroups:
  - torin.moments8.com
  resources:
  - imagewarmups/status
  verbs:
  - get
  - patch
  - update
---
# Metrics Auth Role
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "image-warmup-operator.metricsAuthRoleName" . }}
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
# Metrics Reader Role
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "image-warmup-operator.metricsReaderRoleName" . }}
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
rules:
- nonResourceURLs:
  - /metrics
  verbs:
  - get
---
# Leader Election RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "image-warmup-operator.leaderElectionRoleName" . }}-binding
  namespace: {{ include "image-warmup-operator.namespace" . }}
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "image-warmup-operator.leaderElectionRoleName" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "image-warmup-operator.serviceAccountName" . }}
  namespace: {{ include "image-warmup-operator.namespace" . }}
---
# Manager ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "image-warmup-operator.managerRoleName" . }}-binding
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "image-warmup-operator.managerRoleName" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "image-warmup-operator.serviceAccountName" . }}
  namespace: {{ include "image-warmup-operator.namespace" . }}
---
# Metrics Auth ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "image-warmup-operator.metricsAuthRoleName" . }}-binding
  labels:
    {{- include "image-warmup-operator.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "image-warmup-operator.metricsAuthRoleName" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "image-warmup-operator.serviceAccountName" . }}
  namespace: {{ include "image-warmup-operator.namespace" . }}
{{- end }}
