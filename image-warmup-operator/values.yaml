# Default values for image-warmup-operator
# This is a YAML-formatted file.

# Global configuration values
global:
  # Override image registry for all images (leave empty to use chart defaults)
  imageRegistry: ""

# Override the chart name (leave empty to use chart name)
nameOverride: ""
# Override the full resource name (leave empty to auto-generate)
fullnameOverride: ""

# Container image configuration
image:
  # Container registry URL
  registry: "harbor.intra.moments8.com"
  # Image repository path
  repository: moments8/torin/image-warmup-operator
  # Image pull policy (Always, IfNotPresent, Never)
  pullPolicy: IfNotPresent
  # Image tag (overrides the image tag whose default is the chart appVersion)
  tag: ""

# Image pull secrets for private registries
imagePullSecrets: []

# ServiceAccount configuration
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use (if not set and create is true, a name is generated)
  name: ""

# Pod annotations
podAnnotations:
  kubectl.kubernetes.io/default-container: manager

# Pod labels
podLabels: {}

# Pod security context
podSecurityContext:
  # Run as non-root user for security
  runAsNonRoot: true

# Container security context
securityContext:
  # Disable privilege escalation
  allowPrivilegeEscalation: false
  # Drop all capabilities
  capabilities:
    drop:
    - "ALL"

# Resource allocation for the operator container
resources:
  # Minimum resources guaranteed to the container
  requests:
    # Minimum CPU allocation (10 millicores)
    cpu: 10m
    # Minimum memory allocation (64MB)
    memory: 64Mi
  # Maximum resources the container can use
  limits:
    # Maximum CPU limit (500 millicores)
    cpu: 500m
    # Maximum memory limit (128MB)
    memory: 128Mi

# Health check configuration
livenessProbe:
  # Enable liveness probe
  enabled: true
  # HTTP GET probe configuration
  httpGet:
    # Health check endpoint path
    path: /healthz
    # Port to check
    port: 8081
  # Delay before first probe after container start
  initialDelaySeconds: 15
  # How often to perform the probe (in seconds)
  periodSeconds: 20

readinessProbe:
  # Enable readiness probe
  enabled: true
  # HTTP GET probe configuration
  httpGet:
    # Readiness check endpoint path
    path: /readyz
    # Port to check
    port: 8081
  # Delay before first probe after container start
  initialDelaySeconds: 5
  # How often to perform the probe (in seconds)
  periodSeconds: 10

# Number of pod replicas (typically 1 for operators)
replicaCount: 1

# Node selection constraints
nodeSelector: {}

# Tolerations for pod assignment
tolerations: []

# Affinity rules for pod assignment
affinity: {}

# Operator configuration
operator:
  # Command line arguments for the operator
  args:
    # Metrics server bind address
    metricsBindAddress: ":8443"
    # Enable leader election for high availability
    leaderElect: true
    # Health probe bind address
    healthProbeBindAddress: ":8081"

# Metrics service configuration
metrics:
  # Enable metrics service
  enabled: true
  # Service configuration
  service:
    # Service type
    type: ClusterIP
    # Service port
    port: 8443
    # Target port on the container
    targetPort: 8443
    # Protocol
    protocol: TCP
    # Port name
    name: https

# RBAC configuration
rbac:
  # Create RBAC resources
  create: true

# Leader election configuration
leaderElection:
  # Enable leader election
  enabled: true

# Termination grace period for pods
terminationGracePeriodSeconds: 10

# Additional labels applied to all resources
additionalLabels:
  app.kubernetes.io/name: image-warmup-operator
  app.kubernetes.io/part-of: torin-system
