# MAAS Operator Helm Chart

A Helm chart for deploying the MAAS Operator, a Kubernetes operator that manages MAAS (Model as a Service) services and inference workloads.

## Description

The MAAS Operator is a Kubernetes controller that:

- **Manages MAAS Services** through custom resources (MaasService CRD)
- **Deploys MAAS clusters** using Helm charts
- **Manages inference services** and their ingress configurations
- **Handles service discovery** and load balancing for inference workloads
- **Provides lifecycle management** for MAAS deployments

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- RBAC enabled cluster
- Access to container registry for MAAS operator image

## Installation

### Quick Start

```bash
# Install with default values
helm install maas-operator ./maas-operator \
  --namespace torin-system \
  --create-namespace

# Install with custom image tag
helm install maas-operator ./maas-operator \
  --namespace torin-system \
  --create-namespace \
  --set image.tag=v1.0.7
```

### Production Installation

```bash
# Install with production configuration
helm install maas-operator ./maas-operator \
  --namespace torin-system \
  --create-namespace \
  --set deployment.replicas=2 \
  --set container.resources.limits.cpu=1000m \
  --set container.resources.limits.memory=512Mi
```

## Configuration

### Key Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `image.registry` | Container registry | `moments8-acr-registry.cn-beijing.cr.aliyuncs.com` |
| `image.repository` | Image repository | `moments8/maas-operator` |
| `image.tag` | Image tag | `""` (uses appVersion) |
| `image.pullPolicy` | Image pull policy | `Always` |

### Deployment Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `deployment.replicas` | Number of replicas | `1` |
| `deployment.strategy.type` | Update strategy | `RollingUpdate` |
| `deployment.revisionHistoryLimit` | Revision history limit | `10` |

### Container Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `container.name` | Container name | `manager` |
| `container.command` | Container command | `["/manager"]` |
| `container.args` | Container arguments | See values.yaml |
| `container.resources.requests.cpu` | CPU request | `10m` |
| `container.resources.requests.memory` | Memory request | `64Mi` |
| `container.resources.limits.cpu` | CPU limit | `500m` |
| `container.resources.limits.memory` | Memory limit | `128Mi` |

### RBAC Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `rbac.create` | Create RBAC resources | `true` |
| `serviceAccount.create` | Create service account | `true` |
| `serviceAccount.name` | Service account name | `""` (auto-generated) |

### CRD Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `crds.install` | Install CRDs | `true` |
| `crds.keep` | Keep CRDs on uninstall | `true` |

### Service Configuration

| Parameter | Description | Default |
|-----------|-------------|---------|
| `service.enabled` | Enable metrics service | `true` |
| `service.type` | Service type | `ClusterIP` |
| `service.ports[0].port` | Metrics port | `8443` |

## Usage

### Verify Installation

```bash
# Check deployment status
kubectl get deployment maas-operator -n torin-system

# Check pods
kubectl get pods -n torin-system -l control-plane=controller-manager

# View logs
kubectl logs -f deployment/maas-operator -n torin-system
```

### Check CRDs

```bash
# Verify CRD installation
kubectl get crd maasservices.torin.moments8.com

# View CRD details
kubectl describe crd maasservices.torin.moments8.com
```

### Create MaasService

```yaml
apiVersion: torin.moments8.com/v1alpha1
kind: MaasService
metadata:
  name: example-maas
  namespace: torin-system
spec:
  maas:
    id: "example-maas"
    namespace: "example-maas-ns"
    helm:
      repository: "oci://harbor.intra.moments8.com/helm-charts/maas"
      version: "0.1.0"
      values: |
        # MAAS configuration
        service:
          type: ClusterIP
        ingress:
          enabled: true
  kubeSecret:
    - name: "kube-config-secret"
  inferServices:
    - name: "text-generation-service"
      namespace: "inference"
    - name: "image-classification-service"
      namespace: "inference"
```

Apply the configuration:

```bash
kubectl apply -f maasservice-example.yaml
```

### Monitor MaasService

```bash
# List MaasServices
kubectl get maasservices -n torin-system

# Check status
kubectl describe maasservice example-maas -n torin-system

# Watch status changes
kubectl get maasservices -n torin-system -w
```

## Health Checks

The operator provides health check endpoints:

```bash
# Port forward to access health endpoints
kubectl port-forward deployment/maas-operator 8081:8081 -n torin-system

# Check health
curl http://localhost:8081/healthz

# Check readiness
curl http://localhost:8081/readyz
```

## Metrics

Access operator metrics:

```bash
# Port forward to metrics port
kubectl port-forward service/maas-operator-metrics-service 8443:8443 -n torin-system

# Get metrics (requires proper TLS setup)
curl -k https://localhost:8443/metrics
```

## Troubleshooting

### Common Issues

1. **Operator not starting**
   ```bash
   kubectl describe pod <pod-name> -n torin-system
   kubectl logs <pod-name> -n torin-system
   ```

2. **RBAC permission issues**
   ```bash
   kubectl auth can-i create maasservices --as=system:serviceaccount:torin-system:maas-operator
   ```

3. **CRD not found**
   ```bash
   kubectl get crd | grep maasservices
   helm upgrade maas-operator ./maas-operator --set crds.install=true
   ```

### Debug Commands

```bash
# Check operator events
kubectl get events -n torin-system --sort-by=.metadata.creationTimestamp

# Check leader election
kubectl get lease -n torin-system

# Check service account
kubectl get serviceaccount maas-operator -n torin-system

# Check RBAC
kubectl get clusterrole | grep maas-operator
kubectl get clusterrolebinding | grep maas-operator
```

## Advanced Configuration

### Custom Node Scheduling

```yaml
nodeSelector:
  node-role.kubernetes.io/control-plane: ""

tolerations:
  - key: node-role.kubernetes.io/control-plane
    operator: Exists
    effect: NoSchedule

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchLabels:
            control-plane: controller-manager
        topologyKey: kubernetes.io/hostname
```

### High Availability

```yaml
deployment:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1

container:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 1000m
      memory: 512Mi
```

## Uninstalling

```bash
# Uninstall the operator
helm uninstall maas-operator --namespace torin-system

# Clean up CRDs (if needed)
kubectl delete crd maasservices.torin.moments8.com

# Clean up namespace
kubectl delete namespace torin-system
```

## Contributing

Please read the contributing guidelines before submitting pull requests.

## License

This chart is licensed under the Apache 2.0 License.
