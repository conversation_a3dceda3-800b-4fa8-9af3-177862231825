# Default values for maas-operator
# This is a YAML-formatted file.

# Global configuration values
global:
  # Override image registry for all images (leave empty to use chart defaults)
  imageRegistry: ""

# Override the chart name (leave empty to use chart name)
nameOverride: ""
# Override the full resource name (leave empty to auto-generate)
fullnameOverride: ""

# Container image configuration
image:
  # Container registry URL
  registry: "harbor.intra.moments8.com"
  # Image repository path
  repository: moments8/torin/maas-operator
  # Image pull policy (Always, IfNotPresent, Never)
  pullPolicy: Always
  # Image tag (overrides the image tag whose default is the chart appVersion)
  tag: ""

# Image pull secrets for private registries
imagePullSecrets:
  - name: registry-secret

# Deployment configuration
deployment:
  # Number of replicas
  replicas: 1
  
  # Revision history limit for the Deployment
  revisionHistoryLimit: 10
  
  # Update strategy for the Deployment
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1

# Pod configuration
pod:
  # Pod annotations
  annotations:
    kubectl.kubernetes.io/default-container: manager
  
  # Pod labels (additional to the default ones)
  labels: {}
  
  # Security context for the pod
  securityContext:
    runAsNonRoot: false
    seccompProfile:
      type: RuntimeDefault
  
  # Termination grace period in seconds
  terminationGracePeriodSeconds: 10

# Container configuration
container:
  # Container name
  name: manager
  
  # Container command
  command:
    - /manager
  
  # Container arguments
  args:
    - --metrics-bind-address=:8443
    - --leader-elect
    - --health-probe-bind-address=:8081
  
  # Container ports
  ports: []
  
  # Security context for the container
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
  
  # Resource allocation for the container
  resources:
    # Minimum resources guaranteed to the container
    requests:
      # Minimum CPU allocation (10 millicores)
      cpu: 10m
      # Minimum memory allocation (64MB)
      memory: 64Mi
    # Maximum resources the container can use
    limits:
      # Maximum CPU limit (500 millicores)
      cpu: 500m
      # Maximum memory limit (128MB)
      memory: 128Mi
  
  # Volume mounts
  volumeMounts: []

# Health checks configuration
healthcheck:
  # Liveness probe configuration
  livenessProbe:
    httpGet:
      path: /healthz
      port: 8081
    initialDelaySeconds: 15
    periodSeconds: 20
  
  # Readiness probe configuration
  readinessProbe:
    httpGet:
      path: /readyz
      port: 8081
    initialDelaySeconds: 5
    periodSeconds: 10

# Volumes configuration
volumes: []

# Node selection and scheduling
nodeSelector:
  # Target nodes with torin components scheduling enabled
  torin-components-scheduling: enable

# Tolerations for pod assignment to nodes
tolerations: []

# Affinity rules for pod assignment (optional)
affinity: {}

# Priority class for the pods
priorityClassName: ""

# Service account configuration
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use (if not set and create is true, a name is generated)
  name: ""

# RBAC configuration
rbac:
  # Create RBAC resources
  create: true

# Service configuration for metrics
service:
  # Enable metrics service
  enabled: true
  # Service type
  type: ClusterIP
  # Service port configuration
  ports:
    - name: https
      port: 8443
      protocol: TCP
      targetPort: 8443

# Additional labels applied to all resources
additionalLabels:
  app.kubernetes.io/name: maas-operator
  app.kubernetes.io/part-of: torin-system

# Custom Resource Definitions
crds:
  # Install CRDs
  install: true
  # Keep CRDs on uninstall
  keep: true
