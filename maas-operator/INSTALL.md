# MAAS Operator Installation Guide

## Prerequisites

### 1. Kubernetes Cluster Requirements

- Kubernetes version 1.19 or higher
- RBAC enabled
- Helm 3.2.0 or higher
- Access to container registry for MAAS operator image

### 2. Permissions

Ensure you have cluster-admin permissions to install CRDs and create cluster-wide RBAC resources.

```bash
# Check permissions
kubectl auth can-i create customresourcedefinitions
kubectl auth can-i create clusterroles
kubectl auth can-i create clusterrolebindings
```

### 3. Container Registry Access

Ensure access to the MAAS operator image registry:

```bash
# Test image pull
docker pull moments8-acr-registry.cn-beijing.cr.aliyuncs.com/moments8/maas-operator:v1.0.7
```

## Installation Methods

### Method 1: Basic Installation

```bash
# Install with default settings
helm install maas-operator ./maas-operator \
  --namespace torin-system \
  --create-namespace
```

### Method 2: Production Installation

```bash
# Install with production configuration
helm install maas-operator ./maas-operator \
  --namespace torin-system \
  --create-namespace \
  --values examples/values-production.yaml \
  --set image.tag=v1.0.7
```

### Method 3: Development Installation

```bash
# Install for development/testing
helm install maas-operator ./maas-operator \
  --namespace torin-system \
  --create-namespace \
  --values examples/values-development.yaml
```

### Method 4: Custom Configuration

Create your own values file:

```yaml
# custom-values.yaml
image:
  tag: "v1.0.7"
  pullPolicy: IfNotPresent

deployment:
  replicas: 2

container:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 1000m
      memory: 512Mi

nodeSelector:
  node-role.kubernetes.io/control-plane: ""

tolerations:
  - key: node-role.kubernetes.io/control-plane
    operator: Exists
    effect: NoSchedule
```

Then install:

```bash
helm install maas-operator ./maas-operator \
  --namespace torin-system \
  --create-namespace \
  --values custom-values.yaml
```

## Post-Installation Verification

### 1. Check Deployment Status

```bash
kubectl get deployment maas-operator -n torin-system
```

Expected output:
```
NAME                               READY   UP-TO-DATE   AVAILABLE   AGE
maas-operator   1/1     1            1           2m
```

### 2. Verify Pods are Running

```bash
kubectl get pods -n torin-system -l control-plane=controller-manager
```

### 3. Check CRDs Installation

```bash
kubectl get crd maasservices.torin.moments8.com
```

### 4. View Logs

```bash
kubectl logs -f deployment/maas-operator -n torin-system
```

### 5. Test Health Endpoints

```bash
# Port forward to access health endpoints
kubectl port-forward deployment/maas-operator 8081:8081 -n torin-system

# In another terminal, check health
curl http://localhost:8081/healthz
curl http://localhost:8081/readyz
```

## Creating Your First MaasService

### 1. Prepare Secrets

Create necessary secrets for MAAS operator:

```bash
# Create kubeconfig secret (replace with actual kubeconfig)
kubectl create secret generic kube-config-secret \
  --from-file=kubeconfig=/path/to/kubeconfig \
  -n torin-system

# Create registry secret (if needed)
kubectl create secret docker-registry harbor-registry-secret \
  --docker-server=harbor.intra.moments8.com \
  --docker-username=robot \
  --docker-password=your-password \
  -n torin-system
```

### 2. Create MaasService

```bash
# Apply the example MaasService
kubectl apply -f examples/maasservice-example.yaml
```

### 3. Monitor MaasService

```bash
# Check MaasService status
kubectl get maasservices -n torin-system

# View detailed status
kubectl describe maasservice example-maas -n torin-system

# Watch for changes
kubectl get maasservices -n torin-system -w
```

## Configuration Examples

### High Availability Setup

```yaml
# values-ha.yaml
deployment:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1

container:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 1000m
      memory: 512Mi

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchLabels:
            control-plane: controller-manager
        topologyKey: kubernetes.io/hostname

priorityClassName: "system-cluster-critical"
```

### Resource-Constrained Environment

```yaml
# values-minimal.yaml
deployment:
  replicas: 1

container:
  resources:
    requests:
      cpu: 10m
      memory: 32Mi
    limits:
      cpu: 100m
      memory: 64Mi

service:
  enabled: false
```

## Upgrade

```bash
# Upgrade to new version
helm upgrade maas-operator ./maas-operator \
  --namespace torin-system \
  --values your-values.yaml

# Upgrade with new image tag
helm upgrade maas-operator ./maas-operator \
  --namespace torin-system \
  --set image.tag=v1.0.8 \
  --reuse-values
```

## Uninstall

```bash
# Uninstall the operator
helm uninstall maas-operator --namespace torin-system

# Clean up CRDs (optional - will remove all MaasService resources)
kubectl delete crd maasservices.torin.moments8.com

# Clean up namespace if no other resources
kubectl delete namespace torin-system
```

## Troubleshooting

### Issue: Operator pods not starting

**Solution:**
```bash
# Check pod events
kubectl describe pod <pod-name> -n torin-system

# Check image pull issues
kubectl get events -n torin-system --sort-by=.metadata.creationTimestamp

# Verify image pull secrets
kubectl get secrets -n torin-system
```

### Issue: CRD not found

**Solution:**
```bash
# Check if CRDs are installed
kubectl get crd | grep maasservices

# Reinstall with CRDs
helm upgrade maas-operator ./maas-operator --set crds.install=true
```

### Issue: RBAC permission denied

**Solution:**
```bash
# Check service account
kubectl get serviceaccount maas-operator -n torin-system

# Check cluster role bindings
kubectl get clusterrolebinding | grep maas-operator

# Verify permissions
kubectl auth can-i create maasservices --as=system:serviceaccount:torin-system:maas-operator
```

### Issue: Leader election conflicts

**Solution:**
```bash
# Check leader election lease
kubectl get lease -n torin-system

# Delete lease to force re-election
kubectl delete lease <lease-name> -n torin-system
```

## Monitoring and Observability

### Metrics

```bash
# Access metrics endpoint
kubectl port-forward service/maas-operator-metrics-service 8443:8443 -n torin-system
curl -k https://localhost:8443/metrics
```

### Logging

```bash
# View operator logs
kubectl logs -f deployment/maas-operator -n torin-system

# View logs for specific container
kubectl logs -f deployment/maas-operator -c manager -n torin-system
```

### Events

```bash
# Monitor operator events
kubectl get events -n torin-system --sort-by=.metadata.creationTimestamp

# Watch for new events
kubectl get events -n torin-system -w
```

For more troubleshooting tips, see the [README.md](README.md) file.
