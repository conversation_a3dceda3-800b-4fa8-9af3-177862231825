apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
    helm.sh/resource-policy: keep
  name: maasservices.torin.moments8.com
spec:
  group: torin.moments8.com
  names:
    kind: MaasService
    listKind: MaasServiceList
    plural: maasservices
    singular: maasservice
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .spec.inferServices[*].name
      name: InferServices
      type: integer
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: MaasService 是 maasservices API 的 Schema
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: MaasServiceSpec 定义 MaasService 的期望状态
            properties:
              env:
                description: 环境变量列表（注入到 Helm values 中）
                items:
                  description: EnvVar 定义环境变量
                  properties:
                    name:
                      description: 环境变量名称
                      type: string
                    value:
                      description: 环境变量值
                      type: string
                  required:
                  - name
                  - value
                  type: object
                type: array
              inferServices:
                description: 要管理的推理服务列表
                items:
                  description: InferServiceConfig 定义推理服务的配置
                  properties:
                    name:
                      description: 推理服务名称（同时作为 InferenceService 和 Service 的名称）
                      type: string
                    namespace:
                      description: InferenceService 所在的命名空间
                      type: string
                  required:
                  - name
                  type: object
                type: array
              ingressAnnotations:
                additionalProperties:
                  type: string
                description: 创建 Ingress 时添加的额外注解
                type: object
              ingressClass:
                description: 创建 Ingress 时使用的 IngressClass
                type: string
              kubeSecret:
                description: Kubernetes 集群访问凭据的 Secret 引用列表
                items:
                  description: KubeSecretRef 定义 Kubernetes 集群访问凭据的 Secret 引用
                  properties:
                    name:
                      description: Secret 名称
                      type: string
                    namespace:
                      description: Secret 所在命名空间（可选，默认为 MaasService 命名空间）
                      type: string
                  required:
                  - name
                  type: object
                type: array
              leaseRenewalInterval:
                description: '服务租约续期间隔（默认: 30s）'
                type: string
              maas:
                description: MAAS 集群和部署相关的配置
                properties:
                  helm:
                    description: 部署 MAAS 项目的 Helm 配置
                    properties:
                      chart:
                        description: Chart 名称或路径（本地路径）
                        type: string
                      imagePullSecrets:
                        description: 镜像拉取 Secrets 列表
                        items:
                          type: string
                        type: array
                      repository:
                        description: OCI 仓库中的 Chart 地址（格式：registry/repo/chart:version）
                        type: string
                      values:
                        description: 额外的 Helm values（YAML 字符串格式）
                        type: string
                      version:
                        description: Chart 版本
                        type: string
                    type: object
                  id:
                    description: |-
                      MAAS 实例 ID，用于标识不同的 MAAS 实例
                      如果未指定，将自动使用 metadata.name + "mas"
                    type: string
                  namespace:
                    description: |-
                      Helm Release 的目标命名空间
                      如果未指定，将自动使用 metadata.namespace + "mas"
                    type: string
                type: object
              purge:
                description: '是否在重新部署前完全清理现有的 Helm release（默认: false）'
                type: boolean
              registrySecret:
                description: 容器镜像仓库认证的 Secret 引用列表
                items:
                  description: RegistrySecretRef 定义容器镜像仓库认证的 Secret 引用
                  properties:
                    name:
                      description: Secret 名称
                      type: string
                    namespace:
                      description: Secret 所在命名空间（可选，默认为 MaasService 命名空间）
                      type: string
                  required:
                  - name
                  type: object
                type: array
            required:
            - inferServices
            - kubeSecret
            - maas
            type: object
          status:
            description: MaasServiceStatus 定义 MaasService 的观察状态
            properties:
              conditions:
                description: 表示 MaasService 状态的最新可观察条件
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              inferServiceStatuses:
                description: 包含每个推理服务的状态
                items:
                  description: InferServiceStatus 定义推理服务的状态
                  properties:
                    ingressName:
                      description: 创建的 Ingress 名称
                      type: string
                    ingressURL:
                      description: 创建的 Ingress URL
                      type: string
                    isReserved:
                      description: 指示服务是否当前被预留
                      type: boolean
                    lastLeaseRenewal:
                      description: 上次租约续期的时间戳
                      format: date-time
                      type: string
                    message:
                      description: 提供状态的额外信息
                      type: string
                    name:
                      description: 推理服务名称
                      type: string
                    namespace:
                      description: InferenceService 所在的命名空间
                      type: string
                    status:
                      description: 推理服务状态（Ready, Pending, Error）
                      type: string
                  required:
                  - isReserved
                  - name
                  - status
                  type: object
                type: array
              lastReconciled:
                description: 上次协调的时间戳
                format: date-time
                type: string
              maasStatus:
                description: 包含 MAAS 部署的状态和访问地址
                properties:
                  endpoints:
                    description: MAAS 系统的访问端点
                    properties:
                      accessURL:
                        description: MAAS 访问地址
                        type: string
                      adminURL:
                        description: MAAS Admin 访问地址
                        type: string
                      apiURL:
                        description: MAAS API 访问地址
                        type: string
                    type: object
                  helmReleaseStatus:
                    description: Helm Release 的状态
                    type: string
                  lastUpdated:
                    description: 上次状态更新的时间戳
                    format: date-time
                    type: string
                  message:
                    description: 提供状态的额外信息
                    type: string
                  status:
                    description: MAAS 系统的运行状态（Ready, Pending, Error）
                    type: string
                type: object
              observedGeneration:
                description: 控制器观察到的 Generation
                format: int64
                type: integer
              phase:
                description: 表示 MaasService 的当前阶段
                type: string
              resolvedSpec:
                description: 经过默认值合并后的完整配置规范
                properties:
                  env:
                    description: 环境变量列表（注入到 Helm values 中）
                    items:
                      description: EnvVar 定义环境变量
                      properties:
                        name:
                          description: 环境变量名称
                          type: string
                        value:
                          description: 环境变量值
                          type: string
                      required:
                      - name
                      - value
                      type: object
                    type: array
                  inferServices:
                    description: 要管理的推理服务列表
                    items:
                      description: InferServiceConfig 定义推理服务的配置
                      properties:
                        name:
                          description: 推理服务名称（同时作为 InferenceService 和 Service 的名称）
                          type: string
                        namespace:
                          description: InferenceService 所在的命名空间
                          type: string
                      required:
                      - name
                      type: object
                    type: array
                  ingressAnnotations:
                    additionalProperties:
                      type: string
                    description: 创建 Ingress 时添加的额外注解
                    type: object
                  ingressClass:
                    description: 创建 Ingress 时使用的 IngressClass
                    type: string
                  kubeSecret:
                    description: Kubernetes 集群访问凭据的 Secret 引用列表
                    items:
                      description: KubeSecretRef 定义 Kubernetes 集群访问凭据的 Secret 引用
                      properties:
                        name:
                          description: Secret 名称
                          type: string
                        namespace:
                          description: Secret 所在命名空间（可选，默认为 MaasService 命名空间）
                          type: string
                      required:
                      - name
                      type: object
                    type: array
                  leaseRenewalInterval:
                    description: '服务租约续期间隔（默认: 30s）'
                    type: string
                  maas:
                    description: MAAS 集群和部署相关的配置
                    properties:
                      helm:
                        description: 部署 MAAS 项目的 Helm 配置
                        properties:
                          chart:
                            description: Chart 名称或路径（本地路径）
                            type: string
                          imagePullSecrets:
                            description: 镜像拉取 Secrets 列表
                            items:
                              type: string
                            type: array
                          repository:
                            description: OCI 仓库中的 Chart 地址（格式：registry/repo/chart:version）
                            type: string
                          values:
                            description: 额外的 Helm values（YAML 字符串格式）
                            type: string
                          version:
                            description: Chart 版本
                            type: string
                        type: object
                      id:
                        description: |-
                          MAAS 实例 ID，用于标识不同的 MAAS 实例
                          如果未指定，将自动使用 metadata.name + "mas"
                        type: string
                      namespace:
                        description: |-
                          Helm Release 的目标命名空间
                          如果未指定，将自动使用 metadata.namespace + "mas"
                        type: string
                    type: object
                  purge:
                    description: '是否在重新部署前完全清理现有的 Helm release（默认: false）'
                    type: boolean
                  registrySecret:
                    description: 容器镜像仓库认证的 Secret 引用列表
                    items:
                      description: RegistrySecretRef 定义容器镜像仓库认证的 Secret 引用
                      properties:
                        name:
                          description: Secret 名称
                          type: string
                        namespace:
                          description: Secret 所在命名空间（可选，默认为 MaasService 命名空间）
                          type: string
                      required:
                      - name
                      type: object
                    type: array
                required:
                - inferServices
                - kubeSecret
                - maas
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}

---
# Source: maas-operator/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: maas-operator
  namespace: torin-system
  labels:
    helm.sh/chart: maas-operator-0.1.0
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/instance: maas-operator
    app.kubernetes.io/version: "v1.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/part-of: torin-system
automountServiceAccountToken: true
---
# Source: maas-operator/templates/rbac.yaml
# ClusterRole for maas-operator manager
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: maas-operator-manager-role
  labels:
    helm.sh/chart: maas-operator-0.1.0
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/instance: maas-operator
    app.kubernetes.io/version: "v1.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/part-of: torin-system
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - serving.kserve.io
  resources:
  - inferenceservices
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - maasservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - maasservices/finalizers
  verbs:
  - update
- apiGroups:
  - torin.moments8.com
  resources:
  - maasservices/status
  verbs:
  - get
  - patch
  - update
---
# Source: maas-operator/templates/rbac.yaml
# ClusterRoleBinding for maas-operator manager
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: maas-operator-manager-rolebinding
  labels:
    helm.sh/chart: maas-operator-0.1.0
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/instance: maas-operator
    app.kubernetes.io/version: "v1.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/part-of: torin-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: maas-operator-manager-role
subjects:
- kind: ServiceAccount
  name: maas-operator
  namespace: torin-system
---
# Source: maas-operator/templates/rbac.yaml
# Role for leader election
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: maas-operator-leader-election-role
  namespace: torin-system
  labels:
    helm.sh/chart: maas-operator-0.1.0
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/instance: maas-operator
    app.kubernetes.io/version: "v1.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/part-of: torin-system
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
# Source: maas-operator/templates/rbac.yaml
# RoleBinding for leader election
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: maas-operator-leader-election-rolebinding
  namespace: torin-system
  labels:
    helm.sh/chart: maas-operator-0.1.0
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/instance: maas-operator
    app.kubernetes.io/version: "v1.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/part-of: torin-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: maas-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: maas-operator
  namespace: torin-system
---
# Source: maas-operator/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: maas-operator-metrics-service
  namespace: torin-system
  labels:
    helm.sh/chart: maas-operator-0.1.0
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/instance: maas-operator
    app.kubernetes.io/version: "v1.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/part-of: torin-system
    control-plane: controller-manager
spec:
  type: ClusterIP
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: 8443
  selector:
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/instance: maas-operator
    control-plane: controller-manager
---
# Source: maas-operator/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: maas-operator
  namespace: torin-system
  labels:
    helm.sh/chart: maas-operator-0.1.0
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/instance: maas-operator
    app.kubernetes.io/version: "v1.0.7"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: maas-operator
    app.kubernetes.io/part-of: torin-system
    control-plane: controller-manager
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: maas-operator
      app.kubernetes.io/instance: maas-operator
      control-plane: controller-manager
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        app.kubernetes.io/name: maas-operator
        app.kubernetes.io/instance: maas-operator
        control-plane: controller-manager
    spec:
      imagePullSecrets:
        - name: registry-secret
      serviceAccountName: maas-operator
      securityContext:
        runAsNonRoot: false
        seccompProfile:
          type: RuntimeDefault
      terminationGracePeriodSeconds: 10
      containers:
      - name: manager
        image: moments8-acr-registry.cn-beijing.cr.aliyuncscom/moments8/maas-operator:v1.0.7
        imagePullPolicy: Always
        command:
        - /manager
        args:
        - "--metrics-bind-address=:8443"
        - "--leader-elect"
        - "--health-probe-bind-address=:8081"
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
      nodeSelector:
        
        torin-components-scheduling: enable
