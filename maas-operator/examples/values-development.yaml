# Development values for maas-operator
# This configuration is optimized for development and testing

# Use latest image for development
image:
  tag: "latest"
  pullPolicy: Always

# Single replica for development
deployment:
  replicas: 1
  revisionHistoryLimit: 3

# Minimal resource allocation for development
container:
  resources:
    requests:
      cpu: 10m
      memory: 32Mi
    limits:
      cpu: 200m
      memory: 128Mi

# Faster health checks for development
healthcheck:
  livenessProbe:
    httpGet:
      path: /healthz
      port: 8081
    initialDelaySeconds: 10
    periodSeconds: 15
    timeoutSeconds: 3
    failureThreshold: 2
  readinessProbe:
    httpGet:
      path: /readyz
      port: 8081
    initialDelaySeconds: 3
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 2

# Relaxed node selector for development
nodeSelector:
  torin-system-scheduling: enable

# Minimal tolerations for development
tolerations: []

# Development-specific pod annotations
pod:
  annotations:
    kubectl.kubernetes.io/default-container: manager
    # Enable debug logging if supported by the application
    debug.level: "verbose"
    # Development environment marker
    environment: "development"

# Enable verbose logging in development
container:
  args:
    - --metrics-bind-address=:8443
    - --leader-elect
    - --health-probe-bind-address=:8081
    - --zap-log-level=debug
    - --zap-development=true
