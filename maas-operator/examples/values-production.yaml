# Production values for maas-operator
# This configuration is optimized for production environments

# Use specific image tag instead of latest
image:
  tag: "v1.0.7"
  pullPolicy: IfNotPresent

# High availability deployment
deployment:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1

# Enhanced resource allocation for production
container:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 1000m
      memory: 512Mi

# Production-grade health checks
healthcheck:
  livenessProbe:
    httpGet:
      path: /healthz
      port: 8081
    initialDelaySeconds: 30
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /readyz
      port: 8081
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

# Node scheduling for control plane nodes
nodeSelector:
  node-role.kubernetes.io/control-plane: ""

# Tolerations for control plane scheduling
tolerations:
  - key: node-role.kubernetes.io/control-plane
    operator: Exists
    effect: NoSchedule
  - key: node-role.kubernetes.io/master
    operator: Exists
    effect: NoSchedule

# Anti-affinity for high availability
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchLabels:
            control-plane: controller-manager
        topologyKey: kubernetes.io/hostname

# Set priority class for critical system components
priorityClassName: "system-cluster-critical"

# Production annotations
pod:
  annotations:
    kubectl.kubernetes.io/default-container: manager
    cluster-autoscaler.kubernetes.io/safe-to-evict: "false"

# Service account with specific annotations for production
serviceAccount:
  annotations:
    # Add any cloud provider specific annotations here
    # eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/MaasOperatorRole"
