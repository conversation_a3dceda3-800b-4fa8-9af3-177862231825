# Example MaasService resource
# This demonstrates how to create a MAAS service with inference services

apiVersion: torin.moments8.com/v1alpha1
kind: MaasService
metadata:
  name: example-maas
  namespace: torin-system
spec:
  # MAAS cluster configuration
  maas:
    id: "example-maas"
    namespace: "example-maas-ns"
    helm:
      # Use OCI repository
      repository: "oci://harbor.intra.moments8.com/helm-charts/maas"
      version: "0.1.0"
      # Custom Helm values for MAAS deployment
      values: |
        # MAAS service configuration
        service:
          type: ClusterIP
          port: 8080
        
        # Ingress configuration
        ingress:
          enabled: true
          className: "nginx"
          annotations:
            nginx.ingress.kubernetes.io/rewrite-target: /
          hosts:
            - host: maas.example.com
              paths:
                - path: /
                  pathType: Prefix
        
        # Resource allocation
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        
        # Persistence
        persistence:
          enabled: true
          size: 10Gi
          storageClass: "fast-ssd"

  # Kubernetes cluster access credentials
  kubeSecret:
    - name: "kube-config-secret"
      # namespace: "default"  # Optional, defaults to MaasService namespace

  # Registry credentials for pulling images
  registrySecret:
    - name: "harbor-registry-secret"

  # Inference services to manage
  inferServices:
    - name: "text-generation-service"
      namespace: "inference"
    - name: "image-classification-service"
      namespace: "inference"
    - name: "speech-recognition-service"
      namespace: "ai-services"

  # Ingress configuration for inference services
  ingressClass: "nginx"
  ingressAnnotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"

  # Service lease renewal interval
  leaseRenewalInterval: "30s"

  # Environment variables to inject into MAAS Helm values
  env:
    - name: "LOG_LEVEL"
      value: "info"
    - name: "METRICS_ENABLED"
      value: "true"
    - name: "CACHE_SIZE"
      value: "1GB"

  # Whether to purge existing Helm release before deployment
  purge: false

---
# Example Secret for Kubernetes cluster access
apiVersion: v1
kind: Secret
metadata:
  name: kube-config-secret
  namespace: torin-system
type: Opaque
data:
  # Base64 encoded kubeconfig content
  kubeconfig: |
    YXBpVmVyc2lvbjogdjEKY2x1c3RlcnM6Ci0gY2x1c3RlcjoKICAgIGNlcnRpZmljYXRlLWF1dGhvcml0eS1kYXRhOiAuLi4KICAgIHNlcnZlcjogaHR0cHM6Ly9rdWJlcm5ldGVzLmV4YW1wbGUuY29tOjY0NDMKICA...

---
# Example Secret for registry access
apiVersion: v1
kind: Secret
metadata:
  name: harbor-registry-secret
  namespace: torin-system
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: |
    ************************************************************************************************************************************************
