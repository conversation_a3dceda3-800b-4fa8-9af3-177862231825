1. Check the MAAS operator status:
  k<PERSON><PERSON><PERSON> get deployment {{ include "maas-operator.fullname" . }} -n {{ .Release.Namespace }}

2. View MAAS operator pods:
  kubectl get pods -l "{{ include "maas-operator.controllerSelectorLabels" . | replace ": " "=" | replace "\n" "," }}" -n {{ .Release.Namespace }}

3. Check MAAS operator logs:
  kubectl logs -f deployment/{{ include "maas-operator.fullname" . }} -n {{ .Release.Namespace }}

4. Verify CRDs installation:
  kubectl get crd maasservices.torin.moments8.com

5. Check MAAS operator health:
  # Health check endpoint
  kubectl port-forward deployment/{{ include "maas-operator.fullname" . }} 8081:8081 -n {{ .Release.Namespace }}
  curl http://localhost:8081/healthz
  curl http://localhost:8081/readyz

{{- if .Values.service.enabled }}
6. Check metrics service:
  kubectl get service {{ include "maas-operator.fullname" . }}-metrics-service -n {{ .Release.Namespace }}
  
  # Access metrics (if accessible)
  kubectl port-forward service/{{ include "maas-operator.fullname" . }}-metrics-service 8443:8443 -n {{ .Release.Namespace }}
  curl -k https://localhost:8443/metrics
{{- end }}

7. Create a MaasService example:
  cat <<EOF | kubectl apply -f -
  apiVersion: torin.moments8.com/v1alpha1
  kind: MaasService
  metadata:
    name: example-maas
    namespace: {{ .Release.Namespace }}
  spec:
    maas:
      id: "example-maas"
      namespace: "example-maas-ns"
      helm:
        repository: "oci://harbor.intra.moments8.com/helm-charts/maas"
        version: "0.1.0"
        values: |
          # Your MAAS Helm values here
    kubeSecret:
      - name: "kube-config-secret"
    inferServices:
      - name: "example-infer-service"
        namespace: "inference-ns"
  EOF

8. Monitor MaasService status:
  kubectl get maasservices -n {{ .Release.Namespace }}
  kubectl describe maasservice example-maas -n {{ .Release.Namespace }}

9. Troubleshooting:
  # Check operator events
  kubectl get events -n {{ .Release.Namespace }} --sort-by=.metadata.creationTimestamp

  # Check RBAC permissions
  kubectl auth can-i create maasservices --as=system:serviceaccount:{{ .Release.Namespace }}:{{ include "maas-operator.serviceAccountName" . }}

  # Check CRD status
  kubectl get crd maasservices.torin.moments8.com -o yaml

MAAS Operator Configuration:
- Image: {{ include "maas-operator.image" . }}
- Replicas: {{ .Values.deployment.replicas }}
- Target nodes: {{ .Values.nodeSelector | toYaml | nindent 2 }}
- Service account: {{ include "maas-operator.serviceAccountName" . }}
- Health probe port: 8081
{{- if .Values.service.enabled }}
- Metrics port: 8443
{{- end }}
- Leader election: enabled
- CRDs installed: {{ .Values.crds.install }}

For more information about MAAS operator, visit:
https://github.com/moments8/maas-operator
