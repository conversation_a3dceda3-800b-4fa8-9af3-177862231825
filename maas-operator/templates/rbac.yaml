{{- if .Values.rbac.create -}}
# ClusterRole for maas-operator manager
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "maas-operator.fullname" . }}-manager-role
  labels:
    {{- include "maas-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - inferenceservices
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - maasservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - torin.moments8.com
  resources:
  - maasservices/finalizers
  verbs:
  - update
- apiGroups:
  - torin.moments8.com
  resources:
  - maasservices/status
  verbs:
  - get
  - patch
  - update
---
# ClusterRoleBinding for maas-operator manager
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "maas-operator.fullname" . }}-manager-rolebinding
  labels:
    {{- include "maas-operator.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "maas-operator.fullname" . }}-manager-role
subjects:
- kind: ServiceAccount
  name: {{ include "maas-operator.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
---
# Role for leader election
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "maas-operator.fullname" . }}-leader-election-role
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "maas-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
# RoleBinding for leader election
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "maas-operator.fullname" . }}-leader-election-rolebinding
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "maas-operator.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "maas-operator.fullname" . }}-leader-election-role
subjects:
- kind: ServiceAccount
  name: {{ include "maas-operator.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
