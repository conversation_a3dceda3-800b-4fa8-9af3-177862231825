{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "maas-operator.fullname" . }}-metrics-service
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "maas-operator.labels" . | nindent 4 }}
    control-plane: controller-manager
spec:
  type: {{ .Values.service.type }}
  ports:
  {{- range .Values.service.ports }}
  - name: {{ .name }}
    port: {{ .port }}
    protocol: {{ .protocol }}
    targetPort: {{ .targetPort }}
  {{- end }}
  selector:
    {{- include "maas-operator.controllerSelectorLabels" . | nindent 4 }}
{{- end }}
