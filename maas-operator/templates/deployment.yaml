apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "maas-operator.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "maas-operator.labels" . | nindent 4 }}
    control-plane: controller-manager
spec:
  replicas: {{ .Values.deployment.replicas }}
  revisionHistoryLimit: {{ .Values.deployment.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "maas-operator.controllerSelectorLabels" . | nindent 6 }}
  {{- with .Values.deployment.strategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      {{- with .Values.pod.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "maas-operator.podLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "maas-operator.serviceAccountName" . }}
      {{- with .Values.pod.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.pod.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ . }}
      {{- end }}
      {{- with .Values.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      containers:
      - name: {{ .Values.container.name }}
        image: {{ include "maas-operator.image" . }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- with .Values.container.command }}
        command:
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- if .Values.container.args }}
        args:
        {{- range .Values.container.args }}
        - {{ . | quote }}
        {{- end }}
        {{- end }}
        {{- with .Values.container.securityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- if .Values.container.ports }}
        ports:
        {{- range .Values.container.ports }}
        - {{ toYaml . | nindent 10 | trim }}
        {{- end }}
        {{- end }}
        {{- with .Values.healthcheck.livenessProbe }}
        livenessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.healthcheck.readinessProbe }}
        readinessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- if .Values.container.resources }}
        resources:
          {{- toYaml .Values.container.resources | nindent 10 }}
        {{- end }}
        {{- if .Values.container.volumeMounts }}
        volumeMounts:
        {{- range .Values.container.volumeMounts }}
        - {{ toYaml . | nindent 10 | trim }}
        {{- end }}
        {{- end }}
      {{- if .Values.volumes }}
      volumes:
      {{- range .Values.volumes }}
      - {{ toYaml . | nindent 8 | trim }}
      {{- end }}
      {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector:
        {{- include "maas-operator.nodeSelector" . | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations:
        {{- include "maas-operator.tolerations" . | nindent 8 }}
      {{- end }}
      {{- if .Values.affinity }}
      affinity:
        {{- include "maas-operator.affinity" . | nindent 8 }}
      {{- end }}
