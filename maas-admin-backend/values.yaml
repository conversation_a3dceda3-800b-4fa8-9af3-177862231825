# Default values for maas-admin-backend
# This is a YAML-formatted file.

# Global configuration values that can be shared across charts
global:
  # Override image registry for all images (leave empty to use chart defaults)
  imageRegistry: ""
  # Deployment environment (dev, test, staging, prod)
  environment: prod
  # Whether this is a Spring Boot application (enables Spring Boot specific features)
  springBoot: true

# Override the chart name (leave empty to use chart name)
nameOverride: ""
# Override the full resource name (leave empty to auto-generate)
fullnameOverride: ""

# Container image configuration
image:
  # Container registry URL
  registry: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com"
  # Image repository path for admin backend
  repository: moments8/maas-admin-backend
  # Image pull policy (Always, IfNotPresent, Never)
  pullPolicy: Always
  # Image tag (use specific version in production instead of 'latest')
  tag: latest

# Number of pod replicas to run (typically 1 for admin services)
replicaCount: 1

# StatefulSet configuration (for stateful applications)
statefulset:
  # Disable StatefulSet (using Deployment for admin backend)
  enabled: false
  # Update strategy for StatefulSet
  updateStrategy:
    # Rolling update type for controlled updates
    type: RollingUpdate
    rollingUpdate:
      # Number of pods to keep during update
      partition: 0

# Deployment configuration (preferred for stateless admin services)
deployment:
  # Enable Deployment for stateless admin backend
  enabled: true
  # Update strategy for Deployment
  updateStrategy:
    # Rolling update type for zero-downtime deployments
    type: RollingUpdate
    rollingUpdate:
      # Maximum number of pods that can be unavailable during update
      maxUnavailable: 0
      # Maximum number of pods that can be created above desired replica count
      maxSurge: 1

# Kubernetes Service configuration
service:
  # Service type (ClusterIP, NodePort, LoadBalancer)
  type: ClusterIP
  # Service port exposed to other services
  port: 80
  # Container port that the service forwards to
  targetPort: 8080
  # Metrics endpoint configuration for monitoring
  metrics:
    # Enable metrics endpoint (for Prometheus scraping)
    enabled: true
    # Port for metrics endpoint
    port: 8082
    # Container port for metrics
    targetPort: 8082

# Environment variables from parent chart
envFromParentChart:
  # Enable to inherit environment variables from parent chart (database credentials, etc.)
  enabled: true

# ConfigMap configuration for admin application settings
configMap:
  # Enable ConfigMap creation
  enabled: true
  # Configuration data as key-value pairs
  data:
    # Database schema name for admin functions
    DB_SCHEMA: admin
    # Application logging level (DEBUG, INFO, WARN, ERROR)
    LOGGING_LEVEL: INFO
    # Server port for the Spring Boot application
    SERVER_PORT: "8080"
    # Single Sign-On (SSO) configuration for admin authentication
    SSO_APP_ID: "67fdd7d2cfdef8ad44139ca0"
    SSO_APP_SECRET: "80"
    # SSO issuer URL for OIDC authentication
    SSO_ISSUER: https://bcpc7idcbgt0.authing.cn/67fdd7d2cfdef8ad44139ca0/oidc
    # SSO redirect URI after authentication (localhost for development)
    SSO_REDIRECT_URI: http://127.0.0.1:8080/callback
    # OpenTelemetry configuration for observability
    OTEL_EXPORTER_OTLP_ENDPOINT: http://alloy.monitoring.svc.cluster.local:4318
    # Disable logs export to reduce overhead
    OTEL_LOGS_EXPORTER: "none"
    # Disable metrics export to reduce overhead (using Prometheus instead)
    OTEL_METRICS_EXPORTER: "none"
    # Add additional key-value pairs here
    # CUSTOM_KEY: "custom_value"

# Additional environment variables for the container
# Example usage:
# extraEnv:
#   - name: FOO
#     value: "bar"
#   - name: SECRET_KEY
#     valueFrom:
#       secretKeyRef:
#         name: my-secret
#         key: secret-key
extraEnv: []

# Kubernetes Secrets configuration
secrets:
  # Enable creation of secrets (for sensitive data like passwords, API keys)
  enabled: false

# External Secrets Operator configuration
externalSecrets:
  # Enable External Secrets for fetching secrets from external systems (AWS, Azure, etc.)
  enabled: false

# Resource allocation for the container
resources:
  # Minimum resources guaranteed to the container
  requests:
    # Minimum memory allocation (2GB for Spring Boot application)
    memory: "2Gi"
    # Minimum CPU allocation (0.1 cores)
    cpu: "100m"
  # Maximum resources the container can use
  limits:
    # Maximum memory limit (2GB)
    memory: "2Gi"
    # Maximum CPU limit (1 core)
    cpu: "1000m"

# Health check configuration for Kubernetes probes
healthcheck:
  # Startup probe - checks if application has started successfully
  startup:
    # Enable startup probe (important for Spring Boot apps with slow startup)
    enabled: true
    # Health check endpoint path
    path: /ping
    # Port to check
    port: 8080
    # Number of failures before considering startup failed (30 * 5s = 2.5 min timeout)
    failureThreshold: 30
    # How often to perform the probe (in seconds)
    periodSeconds: 5

  # Liveness probe - checks if application is running properly
  liveness:
    # Enable liveness probe
    enabled: true
    # Health check endpoint path
    path: /ping
    # Port to check
    port: 8080
    # Delay before first probe after container start
    initialDelaySeconds: 0
    # How often to perform the probe (in seconds)
    periodSeconds: 10
    # Timeout for each probe attempt
    timeoutSeconds: 5
    # Number of consecutive failures before restarting container
    failureThreshold: 3

  # Readiness probe - checks if application is ready to receive traffic
  readiness:
    # Enable readiness probe
    enabled: true
    # Health check endpoint path
    path: /ping
    # Port to check
    port: 8080
    # Delay before first probe after container start
    initialDelaySeconds: 0
    # How often to perform the probe (in seconds)
    periodSeconds: 10
    # Timeout for each probe attempt
    timeoutSeconds: 5
    # Number of consecutive failures before removing from service endpoints
    failureThreshold: 3

# Ingress configuration for external access to admin API
ingress:
  # Enable ingress to expose the admin service externally
  enabled: true
  # Ingress class (alb for AWS Application Load Balancer)
  className: alb
  # Ingress annotations for load balancer configuration
  annotations:
    # Rewrite URL path to remove /admin/api prefix before forwarding to backend
    alb.ingress.kubernetes.io/rewrite-target: '/${1}'
    # Configure ALB to listen on both HTTP and HTTPS ports
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80},{"HTTPS": 443}]'
    # Automatically redirect HTTP traffic to HTTPS
    alb.ingress.kubernetes.io/ssl-redirect: 'true'
    # Enable regex support for path matching
    alb.ingress.kubernetes.io/use-regex: 'true'
    # Rate limiting for admin API (500 queries per second)
    alb.ingress.kubernetes.io/traffic-limit-qps: "500"
  # Hostname for the ingress (leave empty to use auto-generated host)
  host: ""
  # Path-based routing configuration
  paths:
    # Route /admin/api/* requests to this admin backend service
    # The (.*) captures everything after /admin/api/ for rewrite-target
    - path: /admin/api/(.*)
      # Path matching type (Prefix with regex support)
      pathType: Prefix


# Prometheus ServiceMonitor for metrics collection
serviceMonitor:
  # Enable ServiceMonitor creation for Prometheus scraping
  enabled: true
  # Additional labels for the ServiceMonitor
  labels: {}
  # Additional annotations for the ServiceMonitor
  annotations: {}
  # Metrics endpoint path (Spring Boot Actuator default)
  path: /actuator/prometheus
  # Service port name to scrape metrics from
  port: metrics
  # How often Prometheus should scrape metrics
  interval: 30s
  # Timeout for each scrape attempt
  scrapeTimeout: 10s

# CronJob configuration for daily database maintenance tasks
cronjob:
  # Enable CronJob creation for database analytics and maintenance
  enabled: true
  # Suspend the CronJob (useful for testing/maintenance)
  suspend: true
  # Cron schedule expression (runs at 2:00 AM every day)
  schedule: "0 2 * * *"
  # Timezone for the cron schedule
  timeZone: "Asia/Shanghai"
  # Number of successful job history to keep
  successfulJobsHistoryLimit: 1
  # Number of failed job history to keep
  failedJobsHistoryLimit: 3
  # Container image for the CronJob (Ubuntu with database tools)
  image:
    # Container registry URL
    registry: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com"
    # Image repository (Ubuntu with PostgreSQL client tools)
    repository: standard/tools
    # Image tag (Ubuntu 24.04)
    tag: ubuntu2404
    # Image pull policy
    pullPolicy: Always
  # Command to execute database maintenance procedures
  command:
    - "bash"
    - "-c"
    - |
      #!/bin/bash
      # Database maintenance script for MAAS admin analytics

      # Check if all required database environment variables are set
      if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ] || [ -z "$DB_USERNAME" ] || [ -z "$DB_PASSWORD" ] || [ -z "$DB_NAME" ]; then
        echo "错误: 请确保所有必要的数据库环境变量已设置"
        echo "Required: DB_HOST, DB_PORT, DB_USERNAME, DB_PASSWORD, DB_NAME"
        exit 1
      fi

      # Generate date parameters for yesterday's data processing
      START_DATE=$(date -d "yesterday" +%Y-%m-%d)T00:00:00
      END_DATE=$(date +%Y-%m-%d)T00:00:00
      echo "开始日期: $START_DATE"
      echo "结束日期: $END_DATE"
      echo "正在执行SQL存储过程..."

      # Create temporary SQL command file
      SQL_CMD=$(mktemp)
      cat << EOF > $SQL_CMD
      BEGIN;

      -- Generate user activity analytics (daily aggregation)
      CALL admin.generate_user_active_count_daily(
      TIMESTAMP '$START_DATE',
      TIMESTAMP '$END_DATE'
      );

      -- Generate user activity analytics (6-hourly aggregation)
      CALL admin.generate_user_active_count_6hourly(
      TIMESTAMP '$START_DATE',
      TIMESTAMP '$END_DATE'
      );

      -- Generate user activity analytics (hourly aggregation)
      CALL admin.generate_user_active_count_hourly(
      TIMESTAMP '$START_DATE',
      TIMESTAMP '$END_DATE'
      );

      -- Generate new user registration analytics (daily)
      CALL admin.generate_user_new_count_daily(
      TIMESTAMP '$START_DATE',
      TIMESTAMP '$END_DATE'
      );

      -- Generate new user registration analytics (hourly)
      CALL admin.generate_user_new_count_hourly(
      TIMESTAMP '$START_DATE',
      TIMESTAMP '$END_DATE'
      );

      -- Generate user transaction history analytics (daily)
      CALL admin.generate_user_transaction_history_daily(
      TIMESTAMP '$START_DATE',
      TIMESTAMP '$END_DATE'
      );

      -- Generate user transaction history analytics (hourly)
      CALL admin.generate_user_transaction_history_hourly(
      TIMESTAMP '$START_DATE',
      TIMESTAMP '$END_DATE'
      );

      -- Archive old transaction history data
      CALL admin.archive_user_transaction_history(
      TIMESTAMP '$START_DATE',
      TIMESTAMP '$END_DATE'
      );

      -- Generate user violation/abuse analytics (daily)
      CALL admin.generate_user_violation_count_daily(
      TIMESTAMP '$START_DATE',
      TIMESTAMP '$END_DATE'
      );

      COMMIT;
      EOF

      # Execute SQL procedures using PostgreSQL client
      # Use environment variable for password (more secure than command line)
      export PGPASSWORD=$DB_PASSWORD
      psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_NAME -f $SQL_CMD

      # Check execution result
      RESULT=$?
      if [ $RESULT -eq 0 ]; then
        echo "SQL存储过程执行成功"
      else
        echo "SQL存储过程执行失败"
        exit 1
      fi

      # Clean up temporary file
      rm -f $SQL_CMD
      exit 0

# Pod annotations for Prometheus scraping (alternative to ServiceMonitor)
podAnnotations:
  # Enable Prometheus scraping for this pod
  prometheus.io/scrape: "true"
  # Metrics endpoint path
  prometheus.io/path: "/actuator/prometheus"
  # Port for metrics scraping
  prometheus.io/port: "8082"
  # Protocol scheme for scraping
  prometheus.io/scheme: "http"

# Additional labels applied to all resources
additionalLabels:
  # Application name label
  app.kubernetes.io/name: maas-admin-backend
  # System/project this application belongs to
  app.kubernetes.io/part-of: maas-system