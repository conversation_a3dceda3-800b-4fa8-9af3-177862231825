# Default values for maas-backend
# This is a YAML-formatted file.

# Global configuration values that can be shared across charts
global:
  # Override image registry for all images (leave empty to use chart defaults)
  imageRegistry: ""
  # Deployment environment (dev, test, staging, prod)
  environment: test
  # Whether this is a Spring Boot application (enables Spring Boot specific features)
  springBoot: true

# Override the chart name (leave empty to use chart name)
nameOverride: ""
# Override the full resource name (leave empty to auto-generate)
fullnameOverride: ""

# Container image configuration
image:
  # Container registry URL
  registry: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com"
  # Image repository path
  repository: moments8/maas-backend
  # Image pull policy (Always, IfNotPresent, Never)
  pullPolicy: Always
  # Image tag (use specific version in production instead of 'latest')
  tag: latest

# Number of pod replicas to run
replicaCount: 1

# StatefulSet configuration (preferred for stateful applications)
statefulset:
  # Enable StatefulSet deployment (provides stable network identity and persistent storage)
  enabled: true
  # Update strategy for StatefulSet
  updateStrategy:
    # Rolling update type for controlled updates
    type: RollingUpdate
    rollingUpdate:
      # Number of pods to keep during update (0 = update all at once)
      partition: 0

# Deployment configuration (alternative to StatefulSet)
deployment:
  # Disable Deployment (using StatefulSet instead)
  enabled: false
  # Update strategy for Deployment
  updateStrategy:
    # Rolling update type for zero-downtime deployments
    type: RollingUpdate
    rollingUpdate:
      # Maximum number of pods that can be unavailable during update
      maxUnavailable: 0
      # Maximum number of pods that can be created above desired replica count
      maxSurge: 1

# Kubernetes Service configuration
service:
  # Service type (ClusterIP, NodePort, LoadBalancer)
  type: ClusterIP
  # Service port exposed to other services
  port: 80
  # Container port that the service forwards to
  targetPort: 8080
  # Metrics endpoint configuration for monitoring
  metrics:
    # Enable metrics endpoint (for Prometheus scraping)
    enabled: true
    # Port for metrics endpoint
    port: 8082
    # Container port for metrics
    targetPort: 8082

# Environment variables from parent chart
envFromParentChart:
  # Enable to inherit environment variables from parent chart (database credentials, etc.)
  enabled: true

# ConfigMap configuration for application settings
configMap:
  # Enable ConfigMap creation
  enabled: true
  # Configuration data as key-value pairs
  data:
    # Database schema name for MAAS application
    DB_SCHEMA: mas
    # Application logging level (DEBUG, INFO, WARN, ERROR)
    LOGGING_LEVEL: INFO
    # Server port for the Spring Boot application
    SERVER_PORT: "8080"
    # SMS service configuration
    SMS_TEMPLATE_CODE: SMS_480195354
    # SMS signature name (Chinese characters for brand name)
    SMS_SIGN_NAME: 矩量无限
    # Whitelist of phone numbers for SMS testing/access control
    WHITE_LIST: "18519191328,13910766765,18850575164,18601089004,13371760072,18846042481,15253159870,13501186880,15911022052,18833004035,13521988877,13910122380,13521891006,16601323312,13621360482,13811101748,18601083205,13910122380,17896009760,18611489680,13601176512,18510957757,13811203653,13051005220,18611105886,13683503202,13436934959,13071131926,13116179785,13811409780"
    # XIMU (AI/ML platform) workspace configuration
    XIMU_WORKSPACE: moments8
    # XIMU workspace unique identifier
    XIMU_WORKSPACE_ID: "c871e622-91d2-45cd-a85b-029273565a20"
    # Auto-scaling configuration for XIMU services
    XIMU_SERVE_SCALE_OUT: "15"      # CPU threshold for scaling out (%)
    XIMU_SERVE_SCALE_IN: "80"       # CPU threshold for scaling in (%)
    XIMU_SERVE_SCALE_STEPS: "2"     # Number of replicas to add/remove per scaling event
    XIMU_SERVE_SCALE_MULTIPLEX: "5" # Maximum concurrent requests per replica
    # Kubernetes configuration file paths for cluster management
    XIMU_KUBE_CONFIG_PATH: /app/config/kubeconfig
    BIREN_KUBE_CONFIG_PATH: /app/config/biren-kubeconfig
    # OpenTelemetry configuration for observability
    OTEL_EXPORTER_OTLP_ENDPOINT: http://alloy.monitoring.svc.cluster.local:4318
    # Disable logs export to reduce overhead
    OTEL_LOGS_EXPORTER: "none"
    # Disable metrics export to reduce overhead (using Prometheus instead)
    OTEL_METRICS_EXPORTER: "none"
    # Add additional key-value pairs here
    # CUSTOM_KEY: "custom_value"

# Additional environment variables for the container
# Example usage:
# extraEnv:
#   - name: FOO
#     value: "bar"
#   - name: SECRET_KEY
#     valueFrom:
#       secretKeyRef:
#         name: my-secret
#         key: secret-key
extraEnv: []

# External Secrets Operator configuration
externalSecrets:
  # Enable External Secrets for fetching secrets from external systems (AWS, Azure, etc.)
  enabled: false

# Kubernetes Secrets configuration
secrets:
  # Enable creation of secrets (for sensitive data like passwords, API keys)
  enabled: false

# Resource allocation for the container
resources:
  # Minimum resources guaranteed to the container
  requests:
    # Minimum memory allocation (2GB for Spring Boot application)
    memory: "2Gi"
    # Minimum CPU allocation (0.1 cores)
    cpu: "100m"
  # Maximum resources the container can use
  limits:
    # Maximum memory limit (2GB)
    memory: "2Gi"
    # Maximum CPU limit (1 core)
    cpu: "1000m"

# Health check configuration for Kubernetes probes
healthcheck:
  # Startup probe - checks if application has started successfully
  startup:
    # Enable startup probe (important for Spring Boot apps with slow startup)
    enabled: true
    # Health check endpoint path
    path: /ping
    # Port to check
    port: 8080
    # Number of failures before considering startup failed (30 * 5s = 2.5 min timeout)
    failureThreshold: 30
    # How often to perform the probe (in seconds)
    periodSeconds: 5

  # Liveness probe - checks if application is running properly
  liveness:
    # Enable liveness probe
    enabled: true
    # Health check endpoint path
    path: /ping
    # Port to check
    port: 8080
    # Delay before first probe after container start
    initialDelaySeconds: 0
    # How often to perform the probe (in seconds)
    periodSeconds: 10
    # Timeout for each probe attempt
    timeoutSeconds: 5
    # Number of consecutive failures before restarting container
    failureThreshold: 3

  # Readiness probe - checks if application is ready to receive traffic
  readiness:
    # Enable readiness probe
    enabled: true
    # Health check endpoint path
    path: /ping
    # Port to check
    port: 8080
    # Delay before first probe after container start
    initialDelaySeconds: 0
    # How often to perform the probe (in seconds)
    periodSeconds: 10
    # Timeout for each probe attempt
    timeoutSeconds: 5
    # Number of consecutive failures before removing from service endpoints
    failureThreshold: 3

# Ingress configuration for external access
ingress:
  # Enable ingress to expose the service externally
  enabled: true
  # Ingress class (alb for AWS Application Load Balancer)
  className: alb
  # Ingress annotations for load balancer configuration
  annotations:
    # Configure ALB to listen on both HTTP and HTTPS ports
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80},{"HTTPS": 443}]'
    # Automatically redirect HTTP traffic to HTTPS
    alb.ingress.kubernetes.io/ssl-redirect: 'true'
    # Enable connection keep-alive for better performance
    alb.ingress.kubernetes.io/backend-keepalive: "true"
    # Set idle timeout for connections (5 minutes)
    alb.ingress.kubernetes.io/idle-timeout: "300"
  # Hostname for the ingress (leave empty to use auto-generated host)
  host: ""
  # Path-based routing configuration
  paths:
    # Route /svc/* requests to this backend service
    - path: /svc
      # Path matching type (Prefix matches /svc/*)
      pathType: Prefix
    # Route /api/* requests to this backend service
    - path: /api
      # Path matching type (Prefix matches /api/*)
      pathType: Prefix


# Prometheus ServiceMonitor for metrics collection
serviceMonitor:
  # Enable ServiceMonitor creation for Prometheus scraping
  enabled: true
  # Additional labels for the ServiceMonitor
  labels: {}
  # Additional annotations for the ServiceMonitor
  annotations: {}
  # Metrics endpoint path (Spring Boot Actuator default)
  path: /actuator/prometheus
  # Service port name to scrape metrics from
  port: metrics
  # How often Prometheus should scrape metrics
  interval: 30s
  # Timeout for each scrape attempt
  scrapeTimeout: 10s

# CronJob configuration for scheduled tasks
cronjob:
  # Enable CronJob creation for background tasks
  enabled: false
  # Suspend the CronJob (useful for testing/maintenance)
  suspend: true
  # Cron schedule expression (every minute in this example)
  schedule: "*/1 * * * *"
  # Timezone for the cron schedule
  timeZone: "Asia/Shanghai"
  # Number of successful job history to keep
  successfulJobsHistoryLimit: 1
  # Number of failed job history to keep
  failedJobsHistoryLimit: 3
  # Container image for the CronJob
  image:
    # Container registry URL
    registry: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com"
    # Image repository (Alpine with curl for HTTP requests)
    repository: standard/alpine/curl
    # Image tag
    tag: latest
    # Image pull policy
    pullPolicy: Always
  # Command to execute in the CronJob
  command:
    - "sh"
    - "-c"
    - |
      # Generate timestamp for the request
      TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

      # Create JSON payload for the scheduler endpoint
      JSON_PAYLOAD=$(cat <<EOF
      {
        "event": "ID_001",
        "message": "hello, world!",
        "timestamp": "$TIMESTAMP"
      }
      EOF
      )

      # Send POST request to the scheduler endpoint
      curl -X POST "http://SERVICE_URL_PLACEHOLDER/scheduler" \
            -H "Content-Type: application/json" \
            --data-binary "$JSON_PAYLOAD"

# Pod annotations for Prometheus scraping (alternative to ServiceMonitor)
podAnnotations:
  # Enable Prometheus scraping for this pod
  prometheus.io/scrape: "true"
  # Metrics endpoint path
  prometheus.io/path: "/actuator/prometheus"
  # Port for metrics scraping
  prometheus.io/port: "8082"
  # Protocol scheme for scraping
  prometheus.io/scheme: "http"

# Additional labels applied to all resources
additionalLabels:
  # System/project this application belongs to
  app.kubernetes.io/part-of: maas-system