{{- define "common.cronjob" -}}
{{- if .Values.cronjob.enabled }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "common.fullname" . }}-cronjob
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  suspend: {{ .Values.cronjob.suspend | default false }}
  schedule: {{ .Values.cronjob.schedule | quote }}
  timeZone: {{ .Values.cronjob.timeZone }}
  successfulJobsHistoryLimit: {{ .Values.cronjob.successfulJobsHistoryLimit }}
  failedJobsHistoryLimit: {{ .Values.cronjob.failedJobsHistoryLimit }}
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: Never
          containers:
          - name: {{ .Chart.Name }}-cronjob
            {{- $image := include "common.getCronjobImage" . }}
            image: "{{ $image }}:{{ .Values.cronjob.image.tag }}"
            imagePullPolicy: {{ .Values.cronjob.image.pullPolicy }}
            {{- $serviceUrl := printf "%s:%s" (include "common.fullname" .) (.Values.service.port | toString) }}
            command:
            {{- range .Values.cronjob.command }}
            - {{ . | replace "SERVICE_URL_PLACEHOLDER" $serviceUrl | quote }}
            {{- end }}
            {{- if or .Values.configMap.enabled .Values.envFromParentChart.enabled .Values.cronjob.envFrom }}
            envFrom:
              {{- if .Values.envFromParentChart.enabled }}
              - configMapRef:
                  name: {{ .Release.Name }}-config
              - secretRef:
                  name: {{ .Release.Name }}-db-cronjob-auth
              {{- end }}
              {{- if .Values.configMap.enabled }}
              - configMapRef:
                  name: {{ include "common.fullname" . }}-config
              {{- end }}
              {{- if .Values.cronjob.envFrom }}
              {{- range .Values.cronjob.envFrom }}
              - {{ if .configMapRef }}configMapRef{{ else if .secretRef }}secretRef{{ end }}:
                  name: {{ include "common.fullname" $ }}-{{ if .configMapRef }}{{ .configMapRef.name }}{{ else if .secretRef }}{{ .secretRef.name }}{{ end }}
              {{- end }}
              {{- end }}
            {{- end }}
{{- end }}
{{- end }}
