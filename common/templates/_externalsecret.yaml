{{- define "common.externalsecret" -}}
{{- if .Values.externalSecrets.enabled }}
{{- range .Values.externalSecrets.secrets }}
---
apiVersion: 'alibabacloud.com/v1alpha1'
kind: ExternalSecret
metadata:
  name: {{ include "common.fullname" $ }}-{{ .name }}
  labels:
    {{- include "common.labels" $ | nindent 4 }}
spec:
  provider: {{ $.Values.externalSecrets.provider }}
  {{- if .dataProcess }}
  dataProcess:
    {{- toYaml .dataProcess | nindent 4 }}
  {{- end }}
  {{- if .data }}
  data:
    {{- toYaml .data | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}
{{- end }}