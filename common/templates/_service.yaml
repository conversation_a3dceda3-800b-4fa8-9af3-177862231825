{{- define "common.service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.fullname" . }}
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
  - name: http
    port: {{ .Values.service.port }}
    targetPort: {{ .Values.service.targetPort }}
    protocol: TCP
  {{- if .Values.service.metrics.enabled }}
  - name: metrics
    port: {{ .Values.service.metrics.port }}
    targetPort: {{ .Values.service.metrics.targetPort }}
    protocol: TCP
  {{- end }}
  selector:
    {{- include "common.selectorLabels" . | nindent 4 }}
{{- end }}