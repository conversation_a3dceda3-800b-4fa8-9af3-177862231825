{{- define "common.statefulset" -}}
{{- if .Values.statefulset.enabled }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "common.fullname" . }}
  labels:
    {{- include "common.labels" . | nindent 4 }}
    {{- with .Values.additionalLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  updateStrategy:
    {{- toYaml .Values.statefulset.updateStrategy | nindent 4 }}
  serviceName: {{ include "common.fullname" . }}
  selector:
    matchLabels:
      {{- include "common.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "common.selectorLabels" . | nindent 8 }}
    spec:
      containers:
      - name: {{ .Chart.Name }}
        {{- $image := include "common.getBaseImage" . }}
        image: "{{ $image }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        ports:
        - containerPort: {{ .Values.service.targetPort }}
        {{- if .Values.service.metrics.enabled }}
        - containerPort: {{ .Values.service.metrics.targetPort }}
        {{- end }}
        {{- if or .Values.configMap.enabled .Values.envFromParentChart.enabled }}
        envFrom:
          {{- if .Values.envFromParentChart.enabled }}
          - configMapRef:
              name: {{ .Release.Name }}-config
          - secretRef:
              name: {{ .Release.Name }}-db-maas-auth
          - secretRef:
              name: {{ .Release.Name }}-jwt-keys
          - secretRef:
              name: {{ .Release.Name }}-email-auth
          {{- end }}
          {{- if .Values.configMap.enabled }}
          - configMapRef:
              name: {{ include "common.fullname" . }}-config
          {{- end }}
        {{- end }}
        env:
        {{- if .Values.global.springBoot }}
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: service.name={{ include "common.fullname" . }},deployment.environment={{ .Values.global.environment }},k8s.namespace.name={{ .Release.Namespace }}
        - name: SPRING_PROFILES_ACTIVE
          value: {{ .Values.global.environment }}
        {{- end}}
        {{- if .Values.extraEnv }}
        {{- toYaml .Values.extraEnv | nindent 8 }}
        {{- end }}
        {{- if .Values.envFromSecret }}
        {{- range $secretGroup := .Values.envFromSecret }}
        {{- range $key := $secretGroup.keys }}
        - name: {{ $key.envName }}
          valueFrom:
            secretKeyRef:
              name: {{ include "common.fullname" $ }}-{{ $secretGroup.secretName }}
              key: {{ $key.secretKey }}
        {{- end }}
        {{- end }}
        {{- end }}
        {{- if .Values.volumeMounts }}
        volumeMounts:
        {{- toYaml .Values.volumeMounts | nindent 8 }}
        {{- end }}
        resources:
          {{- toYaml .Values.resources | nindent 10 }}
        {{- if .Values.healthcheck.startup.enabled }}
        startupProbe:
          httpGet:
            path: {{ .Values.healthcheck.startup.path }}
            port: {{ .Values.healthcheck.startup.port }}
          failureThreshold: {{ .Values.healthcheck.startup.failureThreshold }}
          periodSeconds: {{ .Values.healthcheck.startup.periodSeconds }}
        {{- end }}
        {{- if .Values.healthcheck.liveness.enabled }}
        livenessProbe:
          httpGet:
            path: {{ .Values.healthcheck.liveness.path }}
            port: {{ .Values.healthcheck.liveness.port }}
          initialDelaySeconds: {{ .Values.healthcheck.liveness.initialDelaySeconds }}
          periodSeconds: {{ .Values.healthcheck.liveness.periodSeconds }}
          timeoutSeconds: {{ .Values.healthcheck.liveness.timeoutSeconds }}
          failureThreshold: {{ .Values.healthcheck.liveness.failureThreshold }}
        {{- end }}
        {{- if .Values.healthcheck.readiness.enabled }}
        readinessProbe:
          httpGet:
            path: {{ .Values.healthcheck.readiness.path }}
            port: {{ .Values.healthcheck.readiness.port }}
          initialDelaySeconds: {{ .Values.healthcheck.readiness.initialDelaySeconds }}
          periodSeconds: {{ .Values.healthcheck.readiness.periodSeconds }}
          timeoutSeconds: {{ .Values.healthcheck.readiness.timeoutSeconds }}
          failureThreshold: {{ .Values.healthcheck.readiness.failureThreshold }}
        {{- end }}
      {{- if .Values.volumes }}
      volumes:
      {{- range .Values.volumes }}
      - name: {{ .name }}
        {{- if .secret }}
        secret:
          secretName: {{ include "common.fullname" $ }}-{{ .secret.secretName }}
          {{- with .secret.defaultMode }}
          defaultMode: {{ . }}
          {{- end }}
        {{- else }}
        {{- omit . "name" | toYaml | nindent 8 }}
        {{- end }}
      {{- end }}
      {{- end }}
{{- end }}
{{- end }}
