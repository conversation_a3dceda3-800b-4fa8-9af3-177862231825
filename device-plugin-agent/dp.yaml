---
# Source: device-plugin-agent/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: device-plugin-agent
  namespace: default
  labels:
    helm.sh/chart: device-plugin-agent-0.1.0
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/instance: device-plugin-agent
    app: device-plugin-agent
    app.kubernetes.io/version: "latest"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/part-of: torin-system
automountServiceAccountToken: true
---
# Source: device-plugin-agent/templates/rbac.yaml
# ClusterRole for device plugin agent
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: device-plugin-agent
  labels:
    helm.sh/chart: device-plugin-agent-0.1.0
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/instance: device-plugin-agent
    app: device-plugin-agent
    app.kubernetes.io/version: "latest"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/part-of: torin-system
rules:
# Device plugin needs to read nodes to understand hardware topology
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - get
  - list
  - watch
# Device plugin may need to read pods for resource allocation
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
# Device plugin may need to create events for debugging
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
# Source: device-plugin-agent/templates/rbac.yaml
# ClusterRoleBinding for device plugin agent
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: device-plugin-agent
  labels:
    helm.sh/chart: device-plugin-agent-0.1.0
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/instance: device-plugin-agent
    app: device-plugin-agent
    app.kubernetes.io/version: "latest"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/part-of: torin-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: device-plugin-agent
subjects:
- kind: ServiceAccount
  name: device-plugin-agent
  namespace: default
---
# Source: device-plugin-agent/templates/daemonset.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: device-plugin-agent
  namespace: default
  labels:
    helm.sh/chart: device-plugin-agent-0.1.0
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/instance: device-plugin-agent
    app: device-plugin-agent
    app.kubernetes.io/version: "latest"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/part-of: torin-system
spec:
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app.kubernetes.io/name: device-plugin-agent
      app.kubernetes.io/instance: device-plugin-agent
      app: device-plugin-agent
  updateStrategy:
    rollingUpdate:
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: device-plugin-agent
        app.kubernetes.io/instance: device-plugin-agent
        app: device-plugin-agent
    spec:
      serviceAccountName: device-plugin-agent
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: device-plugin-agent
        image: harbor.intra.moments8.com/moments8/torin/device-plugin-agent:latest
        imagePullPolicy: Always
        securityContext:
          privileged: true
        resources:
          limits:
            cpu: 200m
            memory: 128Mi
          requests:
            cpu: 50m
            memory: 64Mi
        volumeMounts:
        - name: device-plugin
          mountPath: /var/lib/kubelet/device-plugins
        - name: pod-resources
          mountPath: /var/lib/kubelet/pod-resources
      volumes:
      - name: device-plugin
        hostPath:
          path: /var/lib/kubelet/device-plugins
          type: ""
      - name: pod-resources
        hostPath:
          path: /data/lib/kubelet/pod-resources
          type: ""
      nodeSelector:
        
        gpu.enable: "true"
      tolerations:
        
        - key: CriticalAddonsOnly
          operator: Exists
        - effect: NoSchedule
          key: node-role.kubernetes.io/master
          operator: Exists
        - effect: NoSchedule
          key: node-role.kubernetes.io/control-plane
          operator: Exists
        - effect: NoSchedule
          key: node.kubernetes.io/not-ready
          operator: Exists
