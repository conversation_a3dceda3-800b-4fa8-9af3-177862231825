---
# Source: device-plugin-agent/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: device-plugin-agent
  namespace: default
  labels:
    helm.sh/chart: device-plugin-agent-0.1.0
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/instance: device-plugin-agent
    app: device-plugin-agent
    app.kubernetes.io/version: "latest"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/part-of: torin-system
automountServiceAccountToken: true
---
# Source: device-plugin-agent/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: device-plugin-agent-config
  namespace: default
  labels:
    helm.sh/chart: device-plugin-agent-0.1.0
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/instance: device-plugin-agent
    app: device-plugin-agent
    app.kubernetes.io/version: "latest"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/part-of: torin-system
data:
  conversion-rules.yaml: |
    mappingRules:
      - sourceDevice: "birentech.com/gpu"
        gpuType: "106C"
        weight: 2
  
      - sourceDevice: "streamcomputing.com/npu"
        gpuType: "STCP920"
        weight: 1
  
      - sourceDevice: "moments8.com/gpu"
        gpuType: "*"
        weight: 1
---
# Source: device-plugin-agent/templates/rbac.yaml
# ClusterRole for device plugin agent
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: device-plugin-agent
  labels:
    helm.sh/chart: device-plugin-agent-0.1.0
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/instance: device-plugin-agent
    app: device-plugin-agent
    app.kubernetes.io/version: "latest"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/part-of: torin-system
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
---
# Source: device-plugin-agent/templates/rbac.yaml
# ClusterRoleBinding for device plugin agent
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: device-plugin-agent
  labels:
    helm.sh/chart: device-plugin-agent-0.1.0
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/instance: device-plugin-agent
    app: device-plugin-agent
    app.kubernetes.io/version: "latest"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/part-of: torin-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: device-plugin-agent
subjects:
- kind: ServiceAccount
  name: device-plugin-agent
  namespace: default
---
# Source: device-plugin-agent/templates/daemonset.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: device-plugin-agent
  namespace: default
  labels:
    helm.sh/chart: device-plugin-agent-0.1.0
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/instance: device-plugin-agent
    app: device-plugin-agent
    app.kubernetes.io/version: "latest"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: device-plugin-agent
    app.kubernetes.io/part-of: torin-system
spec:
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app.kubernetes.io/name: device-plugin-agent
      app.kubernetes.io/instance: device-plugin-agent
      app: device-plugin-agent
  updateStrategy:
    rollingUpdate:
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: device-plugin-agent
        app.kubernetes.io/instance: device-plugin-agent
        app: device-plugin-agent
    spec:
      serviceAccountName: device-plugin-agent
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
      - name: device-plugin-agent
        image: harbor.intra.moments8.com/moments8/torin/device-plugin-agent:latest
        imagePullPolicy: Always
        securityContext:
          privileged: true
        resources:
          limits:
            cpu: 200m
            memory: 128Mi
          requests:
            cpu: 50m
            memory: 64Mi
        volumeMounts:
        - name: device-plugin
          mountPath: /var/lib/kubelet/device-plugins
        - name: pod-resources
          mountPath: /var/lib/kubelet/pod-resources
        - name: config
          mountPath: /etc/device-weight-config
          readOnly: true
      volumes:
      - name: device-plugin
        hostPath:
          path: /var/lib/kubelet/device-plugins
          type: ""
      - name: pod-resources
        hostPath:
          path: /data/lib/kubelet/pod-resources
          type: ""
      - name: config
        configMap:
          name: device-plugin-agent-config
          defaultMode: 0644
      nodeSelector:
        
        gpu.enable: "true"
      tolerations:
        
        - key: CriticalAddonsOnly
          operator: Exists
        - effect: NoSchedule
          key: node-role.kubernetes.io/master
          operator: Exists
        - effect: NoSchedule
          key: node-role.kubernetes.io/control-plane
          operator: Exists
        - effect: NoSchedule
          key: node.kubernetes.io/not-ready
          operator: Exists
