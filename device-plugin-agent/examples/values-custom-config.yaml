# Example values for device-plugin-agent with custom ConfigMap configuration
# This shows how to customize the device weight mapping rules

# Enable and customize ConfigMap
configMap:
  enabled: true
  # Use custom ConfigMap name
  name: "custom-device-weight-config"
  # Custom configuration data
  data:
    # Device weight conversion rules
    conversion-rules.yaml: |
      mappingRules:
        # Biren GPU with higher weight
        - sourceDevice: "birentech.com/gpu"
          gpuType: "106C"
          weight: 4

        # Stream Computing NPU
        - sourceDevice: "streamcomputing.com/npu"
          gpuType: "STCP920"
          weight: 2

        # Moments8 GPU (default weight)
        - sourceDevice: "moments8.com/gpu"
          gpuType: "*"
          weight: 1

        # NVIDIA GPU support
        - sourceDevice: "nvidia.com/gpu"
          gpuType: "A100"
          weight: 8

        - sourceDevice: "nvidia.com/gpu"
          gpuType: "V100"
          weight: 6

        - sourceDevice: "nvidia.com/gpu"
          gpuType: "T4"
          weight: 3

        # AMD GPU support
        - sourceDevice: "amd.com/gpu"
          gpuType: "MI100"
          weight: 5

        - sourceDevice: "amd.com/gpu"
          gpuType: "*"
          weight: 2

    # Additional configuration file
    device-config.yaml: |
      devicePlugin:
        # Device discovery settings
        discovery:
          interval: 30s
          timeout: 10s
        
        # Resource allocation settings
        allocation:
          strategy: "balanced"
          maxDevicesPerPod: 8
        
        # Health check settings
        healthCheck:
          enabled: true
          interval: 60s
          failureThreshold: 3

    # Logging configuration
    logging.yaml: |
      logging:
        level: "info"
        format: "json"
        output: "stdout"
        
        # Component-specific log levels
        components:
          discovery: "debug"
          allocation: "info"
          health: "warn"

# Custom mount path for configuration
volumes:
  config:
    mountPath: /etc/device-plugin-agent/config

# Use specific image version
image:
  tag: "v1.0.0"
  pullPolicy: IfNotPresent

# Enhanced resource allocation
container:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 256Mi
