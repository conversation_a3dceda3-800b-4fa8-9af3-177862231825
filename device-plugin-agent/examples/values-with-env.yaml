# Example values for device-plugin-agent with custom environment variables
# This shows how to add additional environment variables to the container

# Default container environment variables (already included)
container:
  env:
    # Node name where the pod is running (default)
    - name: NODE_NAME
      valueFrom:
        fieldRef:
          fieldPath: spec.nodeName
    
    # Add additional container-specific environment variables
    - name: LOG_LEVEL
      value: "debug"
    
    - name: DEVICE_PLUGIN_PATH
      value: "/var/lib/kubelet/device-plugins"

# Additional environment variables using extraEnv
extraEnv:
  # Custom application settings
  - name: PLUGIN_NAME
    value: "device-plugin-agent"
  
  - name: PLUGIN_VERSION
    value: "v1.0.0"
  
  # Kubernetes metadata
  - name: POD_NAME
    valueFrom:
      fieldRef:
        fieldPath: metadata.name
  
  - name: POD_NAMESPACE
    valueFrom:
      fieldRef:
        fieldPath: metadata.namespace
  
  - name: POD_IP
    valueFrom:
      fieldRef:
        fieldPath: status.podIP
  
  # Resource limits as environment variables
  - name: MEMORY_LIMIT
    valueFrom:
      resourceFieldRef:
        resource: limits.memory
        divisor: "1Mi"
  
  - name: CPU_LIMIT
    valueFrom:
      resourceFieldRef:
        resource: limits.cpu
        divisor: "1m"
  
  # Configuration from ConfigMap
  - name: CONFIG_PATH
    value: "/etc/device-weight-config"
  
  - name: RULES_FILE
    value: "conversion-rules.yaml"
  
  # Example secret reference
  # - name: API_KEY
  #   valueFrom:
  #     secretKeyRef:
  #       name: device-plugin-secrets
  #       key: api-key
  
  # Example configmap reference
  # - name: EXTERNAL_CONFIG
  #   valueFrom:
  #     configMapKeyRef:
  #       name: external-config
  #       key: config.yaml

# Enhanced resource allocation for better environment variable examples
container:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 256Mi

# Enable ConfigMap for configuration files
configMap:
  enabled: true
  data:
    conversion-rules.yaml: |
      mappingRules:
        - sourceDevice: "nvidia.com/gpu"
          gpuType: "A100"
          weight: 8
        - sourceDevice: "nvidia.com/gpu"
          gpuType: "V100"
          weight: 6
        - sourceDevice: "birentech.com/gpu"
          gpuType: "106C"
          weight: 4

# Use specific image version
image:
  tag: "v1.0.0"
  pullPolicy: IfNotPresent
