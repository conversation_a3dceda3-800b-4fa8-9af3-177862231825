# Example pod that requests GPU resources
# Use this to test if the device plugin agent is working correctly

apiVersion: v1
kind: Pod
metadata:
  name: gpu-test-pod
  namespace: default
  labels:
    app: gpu-test
spec:
  containers:
  - name: gpu-container
    image: nvidia/cuda:11.0-base
    command: ["sleep", "3600"]
    resources:
      limits:
        # Request 1 GPU (adjust based on your device plugin's resource name)
        nvidia.com/gpu: 1
      requests:
        cpu: 100m
        memory: 128Mi
  nodeSelector:
    # Ensure pod is scheduled on GPU-enabled nodes
    gpu.enable: "true"
  tolerations:
  # Add tolerations if your GPU nodes have taints
  - key: nvidia.com/gpu
    operator: Exists
    effect: NoSchedule

---
# Example deployment that uses multiple GPU pods
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gpu-workload
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: gpu-workload
  template:
    metadata:
      labels:
        app: gpu-workload
    spec:
      containers:
      - name: gpu-worker
        image: nvidia/cuda:11.0-base
        command: ["sleep", "3600"]
        resources:
          limits:
            nvidia.com/gpu: 1
          requests:
            cpu: 200m
            memory: 256Mi
      nodeSelector:
        gpu.enable: "true"
      tolerations:
      - key: nvidia.com/gpu
        operator: Exists
        effect: NoSchedule
