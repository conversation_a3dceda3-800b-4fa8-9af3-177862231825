# Production values for device-plugin-agent
# This configuration is optimized for production environments

# Use specific image tag instead of latest
image:
  tag: "v1.0.0"
  pullPolicy: IfNotPresent

# Enhanced resource allocation for production
container:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 256Mi

# Enable health checks for better monitoring
healthcheck:
  enabled: true
  livenessProbe:
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3
  readinessProbe:
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

# Production-grade update strategy
daemonset:
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1

# Enhanced tolerations for production clusters
tolerations:
  - key: CriticalAddonsOnly
    operator: Exists
  - effect: NoSchedule
    key: node-role.kubernetes.io/master
    operator: Exists
  - effect: NoSchedule
    key: node-role.kubernetes.io/control-plane
    operator: Exists
  - effect: NoSchedule
    key: node.kubernetes.io/not-ready
    operator: Exists
  - effect: NoSchedule
    key: node.kubernetes.io/unreachable
    operator: Exists
  - effect: NoSchedule
    key: node.kubernetes.io/disk-pressure
    operator: Exists
  - effect: NoSchedule
    key: node.kubernetes.io/memory-pressure
    operator: Exists

# Production node selector (more specific)
nodeSelector:
  gpu.enable: "true"
  node-role.kubernetes.io/worker: ""

# Set priority class for critical system components
priorityClassName: "system-node-critical"

# Add monitoring annotations
pod:
  annotations:
    prometheus.io/scrape: "false"  # Device plugins typically don't expose metrics
    cluster-autoscaler.kubernetes.io/safe-to-evict: "false"

# Service account with specific annotations for production
serviceAccount:
  annotations:
    # Add any cloud provider specific annotations here
    # eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/DevicePluginRole"
