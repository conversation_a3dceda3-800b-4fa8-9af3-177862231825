# Development values for device-plugin-agent
# This configuration is optimized for development and testing

# Use latest image for development
image:
  tag: "latest"
  pullPolicy: Always

# Minimal resource allocation for development
container:
  resources:
    requests:
      cpu: 10m
      memory: 32Mi
    limits:
      cpu: 100m
      memory: 64Mi

# Disable health checks for faster startup in development
healthcheck:
  enabled: false

# Faster update strategy for development
daemonset:
  revisionHistoryLimit: 2
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 2

# Relaxed node selector for development (any node with GPU)
nodeSelector:
  gpu.enable: "true"

# Minimal tolerations for development
tolerations:
  - key: CriticalAddonsOnly
    operator: Exists
  - effect: NoSchedule
    key: node-role.kubernetes.io/master
    operator: Exists
  - effect: NoSchedule
    key: node-role.kubernetes.io/control-plane
    operator: Exists

# Development-specific pod annotations
pod:
  annotations:
    # Enable debug logging if supported by the application
    debug.level: "verbose"
    # Development environment marker
    environment: "development"

# Custom namespace for development
namespace:
  create: true
  name: "device-plugin-dev"
