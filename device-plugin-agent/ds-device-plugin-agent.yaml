apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    app: device-plugin-agent
  name: device-plugin-agent
  namespace: torin-system
spec:
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: device-plugin-agent
  template:
    metadata:
      labels:
        app: device-plugin-agent
    spec:
      containers:
      - image: harbor.intra.moments8.com/moments8/apps/device-plugin-agent:latest
        imagePullPolicy: Always
        name: device-plugin-agent
        resources: {}
        securityContext:
          privileged: true
        volumeMounts:
        - mountPath: /var/lib/kubelet/device-plugins
          name: device-plugin
        - mountPath: /var/lib/kubelet/pod-resources
          name: pod-resources
      hostNetwork: true
      nodeSelector:
        gpu.enable: "true"
      tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      - effect: NoSchedule
        key: node-role.kubernetes.io/master
        operator: Exists
      - effect: NoSchedule
        key: node.kubernetes.io/not-ready
        operator: Exists
      volumes:
      - hostPath:
          path: /var/lib/kubelet/device-plugins
          type: ""
        name: device-plugin
      - hostPath:
          path: /data/lib/kubelet/pod-resources
          type: ""
        name: pod-resources      