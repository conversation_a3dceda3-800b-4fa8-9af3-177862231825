{{- if .Values.rbac.create -}}
# ClusterRole for device plugin agent
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "device-plugin-agent.fullname" . }}
  labels:
    {{- include "device-plugin-agent.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
---
# ClusterRoleBinding for device plugin agent
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "device-plugin-agent.fullname" . }}
  labels:
    {{- include "device-plugin-agent.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "device-plugin-agent.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "device-plugin-agent.serviceAccountName" . }}
  namespace: {{ include "device-plugin-agent.namespace" . }}
{{- end }}
