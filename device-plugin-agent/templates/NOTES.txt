1. Check the device plugin agent status:
  kube<PERSON><PERSON> get daemonset {{ include "device-plugin-agent.fullname" . }} -n {{ include "device-plugin-agent.namespace" . }}

2. View device plugin agent pods:
  kubectl get pods -l "{{ include "device-plugin-agent.selectorLabels" . | replace ": " "=" | replace "\n" "," }}" -n {{ include "device-plugin-agent.namespace" . }}

3. Check device plugin agent logs:
  kubectl logs -f daemonset/{{ include "device-plugin-agent.fullname" . }} -n {{ include "device-plugin-agent.namespace" . }}

4. Verify device plugin registration:
  # Check if device plugins are registered with kubelet
  kubectl get nodes -o json | jq '.items[].status.allocatable'

5. Check device plugin sockets:
  # On a node where the agent is running
  ls -la /var/lib/kubelet/device-plugins/

6. Check ConfigMap configuration:
{{- if .Values.configMap.enabled }}
  kubectl get configmap {{ include "device-plugin-agent.configMapName" . }} -n {{ include "device-plugin-agent.namespace" . }}
  kubectl describe configmap {{ include "device-plugin-agent.configMapName" . }} -n {{ include "device-plugin-agent.namespace" . }}
{{- else }}
  # ConfigMap is disabled in current configuration
{{- end }}

7. Check environment variables:
  # View environment variables in running pods
  kubectl exec -it <pod-name> -n {{ include "device-plugin-agent.namespace" . }} -- env | grep -E "(NODE_NAME|PLUGIN|CONFIG)"

8. Monitor device allocation:
  # Check which pods are using devices
  kubectl describe nodes | grep -A 10 "Allocated resources"

9. Troubleshooting:
  # Check if nodes have the required label
  kubectl get nodes --show-labels | grep gpu.enable

  # Check if pods are scheduled on correct nodes
  kubectl get pods -n {{ include "device-plugin-agent.namespace" . }} -o wide

  # Check device plugin events
  kubectl get events -n {{ include "device-plugin-agent.namespace" . }} --sort-by=.metadata.creationTimestamp

Device Plugin Agent Configuration:
- Target nodes: {{ .Values.nodeSelector | toYaml | nindent 2 }}
- Host network: {{ .Values.pod.hostNetwork }}
- Privileged mode: {{ .Values.container.securityContext.privileged }}
- Device plugin path: {{ .Values.volumes.devicePlugin.hostPath }}
- Pod resources path: {{ .Values.volumes.podResources.hostPath }}
{{- if .Values.configMap.enabled }}
- ConfigMap enabled: {{ .Values.configMap.enabled }}
- ConfigMap name: {{ include "device-plugin-agent.configMapName" . }}
- Config mount path: {{ .Values.volumes.config.mountPath }}
{{- else }}
- ConfigMap enabled: {{ .Values.configMap.enabled }}
{{- end }}

For more information about device plugins, visit:
https://kubernetes.io/docs/concepts/extend-kubernetes/compute-storage-net/device-plugins/
