apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ include "device-plugin-agent.fullname" . }}
  namespace: {{ include "device-plugin-agent.namespace" . }}
  labels:
    {{- include "device-plugin-agent.labels" . | nindent 4 }}
spec:
  revisionHistoryLimit: {{ .Values.daemonset.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "device-plugin-agent.selectorLabels" . | nindent 6 }}
  {{- with .Values.daemonset.updateStrategy }}
  updateStrategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  template:
    metadata:
      {{- with .Values.pod.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "device-plugin-agent.podLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "device-plugin-agent.serviceAccountName" . }}
      {{- if .Values.pod.hostNetwork }}
      hostNetwork: {{ .Values.pod.hostNetwork }}
      {{- end }}
      {{- if .Values.pod.dnsPolicy }}
      dnsPolicy: {{ .Values.pod.dnsPolicy }}
      {{- end }}
      {{- with .Values.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      containers:
      - name: {{ .Values.container.name }}
        image: {{ include "device-plugin-agent.image" . }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- with .Values.container.securityContext }}
        securityContext:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- if .Values.container.resources }}
        resources:
          {{- toYaml .Values.container.resources | nindent 10 }}
        {{- end }}
        volumeMounts:
        - name: device-plugin
          mountPath: {{ .Values.volumes.devicePlugin.mountPath }}
        - name: pod-resources
          mountPath: {{ .Values.volumes.podResources.mountPath }}
        {{- if .Values.healthcheck.enabled }}
        {{- with .Values.healthcheck.livenessProbe }}
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f device-plugin-agent"
          initialDelaySeconds: {{ .initialDelaySeconds }}
          periodSeconds: {{ .periodSeconds }}
          timeoutSeconds: {{ .timeoutSeconds }}
          failureThreshold: {{ .failureThreshold }}
        {{- end }}
        {{- with .Values.healthcheck.readinessProbe }}
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f device-plugin-agent"
          initialDelaySeconds: {{ .initialDelaySeconds }}
          periodSeconds: {{ .periodSeconds }}
          timeoutSeconds: {{ .timeoutSeconds }}
          failureThreshold: {{ .failureThreshold }}
        {{- end }}
        {{- end }}
      volumes:
      - name: device-plugin
        hostPath:
          path: {{ .Values.volumes.devicePlugin.hostPath }}
          type: ""
      - name: pod-resources
        hostPath:
          path: {{ .Values.volumes.podResources.hostPath }}
          type: ""
      {{- if .Values.nodeSelector }}
      nodeSelector:
        {{- include "device-plugin-agent.nodeSelector" . | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations:
        {{- include "device-plugin-agent.tolerations" . | nindent 8 }}
      {{- end }}
      {{- if .Values.affinity }}
      affinity:
        {{- include "device-plugin-agent.affinity" . | nindent 8 }}
      {{- end }}
