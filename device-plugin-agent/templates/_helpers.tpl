{{/*
Expand the name of the chart.
*/}}
{{- define "device-plugin-agent.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "device-plugin-agent.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "device-plugin-agent.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "device-plugin-agent.labels" -}}
helm.sh/chart: {{ include "device-plugin-agent.chart" . }}
{{ include "device-plugin-agent.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- with .Values.additionalLabels }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "device-plugin-agent.selectorLabels" -}}
app.kubernetes.io/name: {{ include "device-plugin-agent.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app: {{ include "device-plugin-agent.name" . }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "device-plugin-agent.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "device-plugin-agent.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the namespace to use
*/}}
{{- define "device-plugin-agent.namespace" -}}
{{- .Release.Namespace }}
{{- end }}

{{/*
Create the image name
*/}}
{{- define "device-plugin-agent.image" -}}
{{- $registry := .Values.global.imageRegistry | default .Values.image.registry }}
{{- $repository := .Values.image.repository }}
{{- $tag := .Values.image.tag | default .Chart.AppVersion }}
{{- if $registry }}
{{- printf "%s/%s:%s" $registry $repository $tag }}
{{- else }}
{{- printf "%s:%s" $repository $tag }}
{{- end }}
{{- end }}

{{/*
Create pod labels
*/}}
{{- define "device-plugin-agent.podLabels" -}}
{{ include "device-plugin-agent.selectorLabels" . }}
{{- with .Values.pod.labels }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Create tolerations
*/}}
{{- define "device-plugin-agent.tolerations" -}}
{{- with .Values.tolerations }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Create node selector
*/}}
{{- define "device-plugin-agent.nodeSelector" -}}
{{- with .Values.nodeSelector }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Create affinity
*/}}
{{- define "device-plugin-agent.affinity" -}}
{{- with .Values.affinity }}
{{ toYaml . }}
{{- end }}
{{- end }}
