# Device Plugin Agent ConfigMap Configuration Guide

## Overview

The Device Plugin Agent Helm chart supports ConfigMap configuration for device weight mapping rules and other configuration settings. This allows you to customize how different GPU and NPU devices are weighted and managed by the device plugin.

## Default Configuration

By default, the chart creates a ConfigMap with device weight mapping rules for common GPU/NPU vendors:

```yaml
configMap:
  enabled: true
  data:
    conversion-rules.yaml: |
      mappingRules:
        - sourceDevice: "birentech.com/gpu"
          gpuType: "106C"
          weight: 2

        - sourceDevice: "streamcomputing.com/npu"
          gpuType: "STCP920"
          weight: 1

        - sourceDevice: "moments8.com/gpu"
          gpuType: "*"
          weight: 1
```

## Configuration Options

### Basic ConfigMap Settings

| Parameter | Description | Default |
|-----------|-------------|---------|
| `configMap.enabled` | Enable/disable ConfigMap creation | `true` |
| `configMap.name` | Custom ConfigMap name | `""` (auto-generated) |
| `configMap.data` | ConfigMap data content | See default rules |
| `volumes.config.mountPath` | Mount path in container | `/etc/device-plugin-agent` |

### Device Weight Mapping Rules

Each mapping rule defines how a specific device type should be weighted:

```yaml
- sourceDevice: "vendor.com/device-type"  # Device resource name
  gpuType: "MODEL_NAME"                   # Specific model or "*" for all
  weight: 2                               # Weight value (higher = more powerful)
```

## Usage Examples

### 1. Install with Default Configuration

```bash
helm install device-plugin-agent ./device-plugin-agent \
  --namespace torin-system \
  --create-namespace
```

### 2. Install with Custom Configuration

```bash
helm install device-plugin-agent ./device-plugin-agent \
  --namespace torin-system \
  --create-namespace \
  --values examples/values-custom-config.yaml
```

### 3. Customize Device Rules Inline

```bash
helm install device-plugin-agent ./device-plugin-agent \
  --namespace torin-system \
  --create-namespace \
  --set-string 'configMap.data.conversion-rules\.yaml=mappingRules:
- sourceDevice: "nvidia.com/gpu"
  gpuType: "A100"
  weight: 8
- sourceDevice: "nvidia.com/gpu"
  gpuType: "V100"
  weight: 6'
```

### 4. Add Additional Configuration Files

```yaml
configMap:
  enabled: true
  data:
    conversion-rules.yaml: |
      mappingRules:
        - sourceDevice: "nvidia.com/gpu"
          gpuType: "A100"
          weight: 8
    
    device-config.yaml: |
      devicePlugin:
        discovery:
          interval: 30s
        allocation:
          strategy: "balanced"
    
    logging.yaml: |
      logging:
        level: "info"
        format: "json"
```

### 5. Use Custom ConfigMap Name

```yaml
configMap:
  enabled: true
  name: "my-custom-device-config"
  data:
    # ... your configuration
```

### 6. Custom Mount Path

```yaml
volumes:
  config:
    mountPath: /custom/config/path

configMap:
  enabled: true
  data:
    # ... your configuration
```

### 7. Disable ConfigMap

```bash
helm install device-plugin-agent ./device-plugin-agent \
  --namespace torin-system \
  --create-namespace \
  --set configMap.enabled=false
```

## Supported Device Types

The configuration supports various GPU and NPU vendors:

### NVIDIA GPUs
```yaml
- sourceDevice: "nvidia.com/gpu"
  gpuType: "A100"
  weight: 8
- sourceDevice: "nvidia.com/gpu"
  gpuType: "V100"
  weight: 6
- sourceDevice: "nvidia.com/gpu"
  gpuType: "T4"
  weight: 3
```

### AMD GPUs
```yaml
- sourceDevice: "amd.com/gpu"
  gpuType: "MI100"
  weight: 5
- sourceDevice: "amd.com/gpu"
  gpuType: "*"
  weight: 2
```

### Biren GPUs
```yaml
- sourceDevice: "birentech.com/gpu"
  gpuType: "106C"
  weight: 4
```

### Stream Computing NPUs
```yaml
- sourceDevice: "streamcomputing.com/npu"
  gpuType: "STCP920"
  weight: 2
```

### Custom Devices
```yaml
- sourceDevice: "moments8.com/gpu"
  gpuType: "*"
  weight: 1
```

## Verification

After installation, verify the ConfigMap:

```bash
# Check ConfigMap exists
kubectl get configmap -n torin-system

# View ConfigMap content
kubectl describe configmap <configmap-name> -n torin-system

# Check if mounted in pods
kubectl exec -it <pod-name> -n torin-system -- ls -la /etc/device-plugin-agent/

# View configuration files
kubectl exec -it <pod-name> -n torin-system -- cat /etc/device-plugin-agent/conversion-rules.yaml
```

## Troubleshooting

### ConfigMap Not Created
- Check if `configMap.enabled` is set to `true`
- Verify Helm template generation: `helm template ... | grep -A 10 ConfigMap`

### Configuration Not Applied
- Verify the ConfigMap is mounted in the pod
- Check the mount path matches your configuration
- Restart the DaemonSet: `kubectl rollout restart daemonset/device-plugin-agent -n torin-system`

### Invalid Configuration
- Validate YAML syntax in your configuration
- Check device plugin logs for configuration errors
- Use `helm template` to preview generated configuration

## Best Practices

1. **Use Specific Image Tags**: Avoid `latest` in production
2. **Validate Configuration**: Test with `helm template` before deployment
3. **Version Control**: Keep your custom values files in version control
4. **Monitor Changes**: Use `helm diff` to preview configuration changes
5. **Backup**: Keep backups of working configurations

## Advanced Configuration

For complex scenarios, you can combine multiple configuration approaches:

```yaml
# values-advanced.yaml
configMap:
  enabled: true
  name: "advanced-device-config"
  data:
    conversion-rules.yaml: |
      # Include from external file or generate dynamically
      {{ .Files.Get "config/conversion-rules.yaml" | nindent 6 }}
    
    runtime-config.yaml: |
      # Runtime configuration
      runtime:
        maxConcurrentDevices: 10
        healthCheckInterval: 60s

volumes:
  config:
    mountPath: /etc/device-plugin-agent/config

# Additional environment variables
extraEnv:
  - name: CONFIG_PATH
    value: "/etc/device-plugin-agent/config"
  - name: RULES_FILE
    value: "conversion-rules.yaml"
```

This provides maximum flexibility for complex deployment scenarios.
