# Default values for kyle-backend
# This is a YAML-formatted file.

# Global configuration values that can be shared across charts
global:
  # Override image registry for all images (leave empty to use chart defaults)
  imageRegistry: ""
  # Deployment environment (dev, staging, prod)
  environment: prod
  # Whether this is a Spring Boot application (affects health check paths)
  springBoot: true

# Override the chart name (leave empty to use chart name)
nameOverride: ""
# Override the full resource name (leave empty to auto-generate)
fullnameOverride: ""

# Container image configuration
image:
  # Container registry URL
  registry: "moments8-acr-registry-vpc.cn-beijing.cr.aliyuncs.com"
  # Image repository path
  repository: moments8/kyle-backend
  # Image pull policy (Always, IfNotPresent, Never)
  pullPolicy: Always
  # Image tag (use specific version in production instead of 'latest')
  tag: latest

# Number of pod replicas to run
replicaCount: 1

# Deployment configuration
deployment:
  # Enable deployment (set to false to use StatefulSet instead)
  enabled: true
  # Update strategy for rolling deployments
  updateStrategy:
    # Rolling update type for zero-downtime deployments
    type: RollingUpdate
    rollingUpdate:
      # Maximum number of pods that can be unavailable during update
      maxUnavailable: 0
      # Maximum number of pods that can be created above desired replica count
      maxSurge: 1

# Kubernetes Service configuration
service:
  # Service type (ClusterIP, NodePort, LoadBalancer)
  type: ClusterIP
  # Service port exposed to other services
  port: 80
  # Container port that the service forwards to
  targetPort: 8080
  # Metrics endpoint configuration for monitoring
  metrics:
    # Enable metrics endpoint (for Prometheus scraping)
    enabled: false
    # Port for metrics endpoint
    port: 8082
    # Container port for metrics
    targetPort: 8082

# Environment variables from parent chart
envFromParentChart:
  # Enable to inherit environment variables from parent chart
  enabled: false

# ConfigMap configuration for application settings
configMap:
  # Enable ConfigMap creation
  enabled: false
  # Configuration data as key-value pairs
  data: {}

# Additional environment variables for the container
# Example usage:
# extraEnv:
#   - name: FOO
#     value: "bar"
#   - name: SECRET_KEY
#     valueFrom:
#       secretKeyRef:
#         name: my-secret
#         key: secret-key
extraEnv: []

# Kubernetes Secrets configuration
secrets:
  # Enable creation of secrets (for sensitive data like passwords, API keys)
  enabled: false

# External Secrets Operator configuration
externalSecrets:
  # Enable External Secrets for fetching secrets from external systems (AWS, Azure, etc.)
  enabled: false

# Resource allocation for the container
resources:
  # Minimum resources guaranteed to the container
  requests:
    # Minimum memory allocation (1GB)
    memory: "1Gi"
    # Minimum CPU allocation (0.1 cores)
    cpu: "100m"
  # Maximum resources the container can use
  limits:
    # Maximum memory limit (1GB)
    memory: "1Gi"
    # Maximum CPU limit (0.5 cores)
    cpu: "500m"

# Health check configuration for Kubernetes probes
healthcheck:
  # Startup probe - checks if application has started successfully
  startup:
    # Enable startup probe
    enabled: false
    # Health check endpoint path
    path: /ping
    # Port to check
    port: 8080
    # Number of failures before considering startup failed
    failureThreshold: 30
    # How often to perform the probe (in seconds)
    periodSeconds: 5

  # Liveness probe - checks if application is running properly
  liveness:
    # Enable liveness probe
    enabled: false
    # Health check endpoint path
    path: /ping
    # Port to check
    port: 8080
    # Delay before first probe after container start
    initialDelaySeconds: 0
    # How often to perform the probe (in seconds)
    periodSeconds: 10
    # Timeout for each probe attempt
    timeoutSeconds: 5
    # Number of consecutive failures before restarting container
    failureThreshold: 3

  # Readiness probe - checks if application is ready to receive traffic
  readiness:
    # Enable readiness probe
    enabled: false
    # Health check endpoint path
    path: /ping
    # Port to check
    port: 8080
    # Delay before first probe after container start
    initialDelaySeconds: 0
    # How often to perform the probe (in seconds)
    periodSeconds: 10
    # Timeout for each probe attempt
    timeoutSeconds: 5
    # Number of consecutive failures before removing from service endpoints
    failureThreshold: 3

# Ingress configuration for external access
ingress:
  # Enable ingress to expose the service externally
  enabled: true
  # Ingress class (alb for AWS Application Load Balancer)
  className: alb
  # Ingress annotations for load balancer configuration
  annotations:
    # Configure ALB to listen on both HTTP and HTTPS ports
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80},{"HTTPS": 443}]'
    # Automatically redirect HTTP traffic to HTTPS
    alb.ingress.kubernetes.io/ssl-redirect: 'true'
  # Hostname for the ingress (leave empty to use auto-generated host)
  host: ""
  # Path-based routing configuration
  paths:
    # Route /api/* requests to this backend service
    - path: /api
      # Path matching type (Prefix matches /api/*)
      pathType: Prefix

# Prometheus ServiceMonitor for metrics collection
serviceMonitor:
  # Enable ServiceMonitor creation for Prometheus scraping
  enabled: false
  # Additional labels for the ServiceMonitor
  labels: {}
  # Additional annotations for the ServiceMonitor
  annotations: {}
  # Metrics endpoint path (Spring Boot Actuator default)
  path: /actuator/prometheus
  # Service port name to scrape metrics from
  port: metrics
  # How often Prometheus should scrape metrics
  interval: 30s
  # Timeout for each scrape attempt
  scrapeTimeout: 10s

# CronJob configuration for scheduled tasks
cronjob:
  # Enable CronJob creation for background tasks
  enabled: false

# Additional labels applied to all resources
additionalLabels:
  # System/project this application belongs to
  app.kubernetes.io/part-of: kyle-system